"use client";

import React, { useMemo } from "react";

type GalleryImage = {
  src: string;
  alt: string;
  width?: number;
  height?: number;
};

// Curated set of 6 images from the public folder
const sampleImages: GalleryImage[] = [
  { src: "/Picture2.jpg", alt: "HVAC installation - exterior unit" },
  { src: "/Picture4.jpg", alt: "Ductwork and air handling system" },
  { src: "/Picture5.jpg", alt: "Thermostat and control wiring" },
  { src: "/Picture6.jpg", alt: "Clean indoor unit installation" },
  { src: "/Picture8.jpg", alt: "Technician onsite maintenance" },
  { src: "/Picture11.jpg", alt: "Smart climate setup overview" },
];

export default function Gallery() {
  const images = useMemo(() => sampleImages, []);

  return (
    <section className="relative py-12 sm:py-16 bg-white">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <header className="relative z-20 mx-auto mb-8 max-w-2xl text-center sm:mb-12">
          <h2 className="text-3xl font-semibold tracking-tight text-black sm:text-4xl">
            Our Gallery
          </h2>
          <p className="mt-3 text-gray-700">
            Explore our recent HVAC installations, maintenance work, and smart climate solutions.
          </p>
        </header>

        {/* Clean, consistent 6-image grid (1/2/3 columns responsive) */}
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {images.map((image, index) => (
            <div
              key={image.src}
              className="group w-full transform rounded-2xl bg-white/70 shadow-sm ring-1 ring-black/5 transition duration-300 hover:-translate-y-0.5 hover:shadow-md dark:bg-white/10 dark:ring-white/10"
            >
              <div className="relative aspect-[4/3] overflow-hidden rounded-2xl">
                <img
                  src={image.src}
                  alt={image.alt}
                  loading="lazy"
                  className="h-full w-full object-cover transition duration-300 group-hover:scale-[1.02] group-hover:brightness-[0.95]"
                />
                <div className="pointer-events-none absolute inset-0 bg-gradient-to-t from-black/40 via-black/5 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
                <div className="pointer-events-none absolute inset-x-0 bottom-0 p-3 text-left text-sm font-medium text-white/90">
                  <span className="inline-block translate-y-2 opacity-0 transition-all duration-300 group-hover:translate-y-0 group-hover:opacity-100">
                    {image.alt}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}


