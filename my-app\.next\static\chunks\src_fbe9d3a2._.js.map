{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/lib/utils.ts"], "sourcesContent": ["import { ClassValue, clsx } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\";\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,yKAAO,EAAC,IAAA,gJAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/ui/typewriter-effect-smooth.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useEffect, useState } from \"react\";\r\n\r\nexport const TypewriterEffectSmooth = ({\r\n  words,\r\n  className,\r\n  cursorClassName,\r\n}: {\r\n  words: {\r\n    text: string;\r\n    className?: string;\r\n  }[];\r\n  className?: string;\r\n  cursorClassName?: string;\r\n}) => {\r\n  const [displayedText, setDisplayedText] = useState(\"\");\r\n  const [currentIndex, setCurrentIndex] = useState(0);\r\n\r\n  // Combine all words into one continuous text\r\n  const fullText = words.map(word => word.text).join(\" \");\r\n  const firstWordClass = words[0]?.className || \"\";\r\n\r\n  useEffect(() => {\r\n    const timeout = setTimeout(() => {\r\n      if (currentIndex < fullText.length) {\r\n        // Typing the full sentence\r\n        setDisplayedText(fullText.substring(0, currentIndex + 1));\r\n        setCurrentIndex(currentIndex + 1);\r\n      }\r\n      // Once fully typed, keep it displayed (no deleting or cycling)\r\n    }, 100); // Typing speed\r\n\r\n    return () => clearTimeout(timeout);\r\n  }, [currentIndex, fullText]);\r\n\r\n  return (\r\n    <div className={cn(\"flex items-center gap-1\", className)}>\r\n      <div className=\"text-xs sm:text-base md:text-xl lg:text-3xl xl:text-5xl font-bold\">\r\n        <span className={cn(\"text-blue-800 font-medium italic\", firstWordClass)}>\r\n          {displayedText}\r\n        </span>\r\n        <span \r\n          className={cn(\r\n            \"inline-block w-0.5 h-4 sm:h-6 xl:h-12 bg-blue-800 ml-1 animate-pulse\",\r\n            cursorClassName\r\n          )}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAKO,MAAM,yBAAyB;QAAC,EACrC,KAAK,EACL,SAAS,EACT,eAAe,EAQhB;QAMwB;;IALvB,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,yKAAQ,EAAC;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yKAAQ,EAAC;IAEjD,6CAA6C;IAC7C,MAAM,WAAW,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,EAAE,IAAI,CAAC;IACnD,MAAM,iBAAiB,EAAA,UAAA,KAAK,CAAC,EAAE,cAAR,8BAAA,QAAU,SAAS,KAAI;IAE9C,IAAA,0KAAS;4CAAC;YACR,MAAM,UAAU;4DAAW;oBACzB,IAAI,eAAe,SAAS,MAAM,EAAE;wBAClC,2BAA2B;wBAC3B,iBAAiB,SAAS,SAAS,CAAC,GAAG,eAAe;wBACtD,gBAAgB,eAAe;oBACjC;gBACA,+DAA+D;gBACjE;2DAAG,MAAM,eAAe;YAExB;oDAAO,IAAM,aAAa;;QAC5B;2CAAG;QAAC;QAAc;KAAS;IAE3B,qBACE,6LAAC;QAAI,WAAW,IAAA,4HAAE,EAAC,2BAA2B;kBAC5C,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAK,WAAW,IAAA,4HAAE,EAAC,oCAAoC;8BACrD;;;;;;8BAEH,6LAAC;oBACC,WAAW,IAAA,4HAAE,EACX,wEACA;;;;;;;;;;;;;;;;;AAMZ;GA/Ca;KAAA", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { TypewriterEffectSmooth } from \"@/components/ui/typewriter-effect-smooth\";\r\nimport Image from \"next/image\";\r\n\r\nconst Hero: React.FC = () => {\r\n  const [currentSlide, setCurrentSlide] = useState(0);\r\n  const [isVisible, setIsVisible] = useState(false);\r\n\r\n  // Enhanced typewriter words with more dynamic content\r\n  const words = [\r\n    { text: \"Comfort\", className: \"text-blue-400 font-bold\" },\r\n    { text: \"You\", className: \"text-white font-bold\" },\r\n    { text: \"Can\", className: \"text-cyan-400 font-bold\" },\r\n    { text: \"Trust\", className: \"text-blue-300 font-bold\" },\r\n  ];\r\n\r\n  // Background images for slideshow\r\n  const backgroundImages = [\r\n    \"/background.jpg\",\r\n    \"/background1.jpg\",\r\n    \"/about1.jpg\",\r\n  ];\r\n\r\n  // Auto-slide effect\r\n  useEffect(() => {\r\n    const interval = setInterval(() => {\r\n      setCurrentSlide((prev) => (prev + 1) % backgroundImages.length);\r\n    }, 6000);\r\n    return () => clearInterval(interval);\r\n  }, [backgroundImages.length]);\r\n\r\n  // Fade in animation on mount\r\n  useEffect(() => {\r\n    setIsVisible(true);\r\n  }, []);\r\n\r\n  // Service highlights\r\n  const services = [\r\n    { icon: \"❄️\", text: \"Air Conditioning\" },\r\n    { icon: \"🔥\", text: \"Heating Systems\" },\r\n    { icon: \"🌬️\", text: \"Air Quality\" },\r\n    { icon: \"🔧\", text: \"Maintenance\" },\r\n  ];\r\n\r\n  return (\r\n    <section\r\n      id=\"hero\"\r\n      className=\"relative w-full h-screen overflow-hidden flex items-center justify-center\"\r\n    >\r\n      {/* Dynamic Background Slideshow */}\r\n      <div className=\"absolute inset-0\">\r\n        {backgroundImages.map((image, index) => (\r\n          <div\r\n            key={index}\r\n            className={`absolute inset-0 transition-opacity duration-1000 ${\r\n              index === currentSlide ? \"opacity-100\" : \"opacity-0\"\r\n            }`}\r\n          >\r\n            <Image\r\n              src={image}\r\n              alt={`HVAC Background ${index + 1}`}\r\n              fill\r\n              priority={index === 0}\r\n              className=\"object-cover\"\r\n            />\r\n          </div>\r\n        ))}\r\n        {/* Enhanced overlay with gradient */}\r\n        <div className=\"absolute inset-0 bg-gradient-to-br from-blue-900/80 via-slate-900/70 to-cyan-900/80\" />\r\n\r\n        {/* Animated particles overlay */}\r\n        <div className=\"absolute inset-0 opacity-30\">\r\n          <div className=\"absolute top-1/4 left-1/4 w-2 h-2 bg-cyan-400 rounded-full animate-pulse\" />\r\n          <div className=\"absolute top-1/3 right-1/3 w-1 h-1 bg-blue-400 rounded-full animate-ping\" />\r\n          <div className=\"absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-white rounded-full animate-pulse\" />\r\n          <div className=\"absolute top-2/3 right-1/4 w-1 h-1 bg-cyan-300 rounded-full animate-ping\" />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Hero Content */}\r\n      <div className={`relative z-10 max-w-6xl mx-auto px-6 sm:px-8 lg:px-12 text-center text-white transition-all duration-1000 ${\r\n        isVisible ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-10\"\r\n      }`}>\r\n\r\n        {/* Company Badge */}\r\n        <div className=\"inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full border border-white/20 mb-6\">\r\n          <span className=\"text-cyan-400 font-semibold text-sm\">🏆 Trusted HVAC Experts Since 2010</span>\r\n        </div>\r\n\r\n        {/* Main Heading with Enhanced Typography */}\r\n        <h1 className=\"text-5xl sm:text-6xl lg:text-7xl xl:text-8xl font-black tracking-tight mb-6\">\r\n          <span className=\"bg-gradient-to-r from-white via-blue-100 to-cyan-200 bg-clip-text text-transparent drop-shadow-2xl\">\r\n            ST HVAC\r\n          </span>\r\n          <br />\r\n          <span className=\"text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-light text-blue-200\">\r\n            SALES & SERVICES\r\n          </span>\r\n        </h1>\r\n\r\n        {/* Enhanced Typewriter Effect */}\r\n        <div className=\"mb-8 flex justify-center\">\r\n          <TypewriterEffectSmooth\r\n            words={words}\r\n            className=\"text-3xl sm:text-4xl lg:text-5xl\"\r\n            cursorClassName=\"bg-cyan-400\"\r\n          />\r\n        </div>\r\n\r\n        {/* Service Icons Row */}\r\n        <div className=\"flex justify-center gap-8 mb-8\">\r\n          {services.map((service, index) => (\r\n            <div\r\n              key={index}\r\n              className=\"flex flex-col items-center group cursor-pointer\"\r\n            >\r\n              <div className=\"text-3xl mb-2 group-hover:scale-110 transition-transform duration-300\">\r\n                {service.icon}\r\n              </div>\r\n              <span className=\"text-sm text-blue-200 group-hover:text-white transition-colors duration-300\">\r\n                {service.text}\r\n              </span>\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Enhanced Description */}\r\n        <p className=\"text-xl sm:text-2xl lg:text-3xl text-blue-100 leading-relaxed max-w-4xl mx-auto mb-10\">\r\n          Delivering{\" \"}\r\n          <span className=\"font-bold text-cyan-300 bg-cyan-300/10 px-2 py-1 rounded\">\r\n            premium comfort solutions\r\n          </span>{\" \"}\r\n          with{\" \"}\r\n          <span className=\"font-bold text-blue-300 bg-blue-300/10 px-2 py-1 rounded\">\r\n            24/7 expert service\r\n          </span>\r\n        </p>\r\n\r\n        {/* Enhanced CTA Buttons */}\r\n        <div className=\"flex flex-col sm:flex-row gap-6 justify-center items-center\">\r\n          <a\r\n            href=\"tel:+15551234567\"\r\n            className=\"group relative px-10 py-5 bg-gradient-to-r from-cyan-500 to-blue-600 text-white rounded-2xl font-bold text-xl shadow-2xl hover:shadow-cyan-500/25 transition-all duration-300 transform hover:-translate-y-2 hover:scale-105\"\r\n          >\r\n            <div className=\"absolute inset-0 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-2xl blur opacity-0 group-hover:opacity-50 transition-opacity duration-300\" />\r\n            <span className=\"relative flex items-center justify-center gap-3\">\r\n              <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                <path d=\"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z\" />\r\n              </svg>\r\n              Call Now: Emergency Service\r\n            </span>\r\n          </a>\r\n\r\n          <a\r\n            href=\"/contact\"\r\n            className=\"group px-10 py-5 border-3 border-cyan-400 text-cyan-400 rounded-2xl font-bold text-xl hover:bg-cyan-400 hover:text-slate-900 transition-all duration-300 transform hover:-translate-y-2 hover:scale-105 shadow-lg hover:shadow-2xl backdrop-blur-sm bg-white/5\"\r\n          >\r\n            <span className=\"flex items-center justify-center gap-3\">\r\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n              </svg>\r\n              Free Quote in 24hrs\r\n            </span>\r\n          </a>\r\n        </div>\r\n\r\n        {/* Trust Indicators */}\r\n        <div className=\"mt-12 flex flex-wrap justify-center gap-8 text-blue-200\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <span className=\"text-yellow-400\">⭐⭐⭐⭐⭐</span>\r\n            <span className=\"text-sm\">500+ Happy Customers</span>\r\n          </div>\r\n          <div className=\"flex items-center gap-2\">\r\n            <span className=\"text-green-400\">✓</span>\r\n            <span className=\"text-sm\">Licensed & Insured</span>\r\n          </div>\r\n          <div className=\"flex items-center gap-2\">\r\n            <span className=\"text-cyan-400\">🕐</span>\r\n            <span className=\"text-sm\">24/7 Emergency Service</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Slide Indicators */}\r\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 flex gap-3 z-20\">\r\n        {backgroundImages.map((_, index) => (\r\n          <button\r\n            key={index}\r\n            onClick={() => setCurrentSlide(index)}\r\n            className={`w-3 h-3 rounded-full transition-all duration-300 ${\r\n              index === currentSlide\r\n                ? \"bg-cyan-400 scale-125\"\r\n                : \"bg-white/50 hover:bg-white/75\"\r\n            }`}\r\n          />\r\n        ))}\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default Hero;\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,MAAM,OAAiB;;IACrB,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yKAAQ,EAAC;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAC;IAE3C,sDAAsD;IACtD,MAAM,QAAQ;QACZ;YAAE,MAAM;YAAW,WAAW;QAA0B;QACxD;YAAE,MAAM;YAAO,WAAW;QAAuB;QACjD;YAAE,MAAM;YAAO,WAAW;QAA0B;QACpD;YAAE,MAAM;YAAS,WAAW;QAA0B;KACvD;IAED,kCAAkC;IAClC,MAAM,mBAAmB;QACvB;QACA;QACA;KACD;IAED,oBAAoB;IACpB,IAAA,0KAAS;0BAAC;YACR,MAAM,WAAW;2CAAY;oBAC3B;mDAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,iBAAiB,MAAM;;gBAChE;0CAAG;YACH;kCAAO,IAAM,cAAc;;QAC7B;yBAAG;QAAC,iBAAiB,MAAM;KAAC;IAE5B,6BAA6B;IAC7B,IAAA,0KAAS;0BAAC;YACR,aAAa;QACf;yBAAG,EAAE;IAEL,qBAAqB;IACrB,MAAM,WAAW;QACf;YAAE,MAAM;YAAM,MAAM;QAAmB;QACvC;YAAE,MAAM;YAAM,MAAM;QAAkB;QACtC;YAAE,MAAM;YAAO,MAAM;QAAc;QACnC;YAAE,MAAM;YAAM,MAAM;QAAc;KACnC;IAED,qBACE,6LAAC;QACC,IAAG;QACH,WAAU;;0BAGV,6LAAC;gBAAI,WAAU;;oBACZ,iBAAiB,GAAG,CAAC,CAAC,OAAO,sBAC5B,6LAAC;4BAEC,WAAW,AAAC,qDAEX,OADC,UAAU,eAAe,gBAAgB;sCAG3C,cAAA,6LAAC,2IAAK;gCACJ,KAAK;gCACL,KAAK,AAAC,mBAA4B,OAAV,QAAQ;gCAChC,IAAI;gCACJ,UAAU,UAAU;gCACpB,WAAU;;;;;;2BAVP;;;;;kCAeT,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;0BAKnB,6LAAC;gBAAI,WAAW,AAAC,6GAEhB,OADC,YAAY,8BAA8B;;kCAI1C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAAsC;;;;;;;;;;;kCAIxD,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;gCAAK,WAAU;0CAAqG;;;;;;0CAGrH,6LAAC;;;;;0CACD,6LAAC;gCAAK,WAAU;0CAAwE;;;;;;;;;;;;kCAM1F,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,uLAAsB;4BACrB,OAAO;4BACP,WAAU;4BACV,iBAAgB;;;;;;;;;;;kCAKpB,6LAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;gCAEC,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;kDACZ,QAAQ,IAAI;;;;;;kDAEf,6LAAC;wCAAK,WAAU;kDACb,QAAQ,IAAI;;;;;;;+BAPV;;;;;;;;;;kCAcX,6LAAC;wBAAE,WAAU;;4BAAwF;4BACxF;0CACX,6LAAC;gCAAK,WAAU;0CAA2D;;;;;;4BAEnE;4BAAI;4BACP;0CACL,6LAAC;gCAAK,WAAU;0CAA2D;;;;;;;;;;;;kCAM7E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;;0DACd,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;0DACnD,cAAA,6LAAC;oDAAK,GAAE;;;;;;;;;;;4CACJ;;;;;;;;;;;;;0CAKV,6LAAC;gCACC,MAAK;gCACL,WAAU;0CAEV,cAAA,6LAAC;oCAAK,WAAU;;sDACd,6LAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCACjE;;;;;;;;;;;;;;;;;;kCAOZ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAkB;;;;;;kDAClC,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAE5B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAiB;;;;;;kDACjC,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAE5B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;0BAMhC,6LAAC;gBAAI,WAAU;0BACZ,iBAAiB,GAAG,CAAC,CAAC,GAAG,sBACxB,6LAAC;wBAEC,SAAS,IAAM,gBAAgB;wBAC/B,WAAW,AAAC,oDAIX,OAHC,UAAU,eACN,0BACA;uBALD;;;;;;;;;;;;;;;;AAYjB;GAnMM;KAAA;uCAqMS", "debugId": null}}, {"offset": {"line": 609, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Image from 'next/image'\n\nconst Client = () => {\n  const clientImages = Array.from({ length: 29 }, (_, i) => `client${i + 1}.jpg`)\n  \n  // Duplicate the array to create seamless loop\n  const duplicatedImages = [...clientImages, ...clientImages]\n\n  return (\n    <section className=\"py-16 bg-white overflow-hidden\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-4xl font-bold text-gray-900 mb-4\">\n            Our Clients\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            We are proud to work with industry leaders and trusted partners who rely on our HVAC expertise and quality service.\n          </p>\n        </div>\n\n        {/* Carousel Container */}\n        <div className=\"relative\">\n          {/* Gradient overlays for fade effect */}\n          <div className=\"absolute left-0 top-0 bottom-0 w-20 bg-gradient-to-r from-white to-transparent z-10\"></div>\n          <div className=\"absolute right-0 top-0 bottom-0 w-20 bg-gradient-to-l from-white to-transparent z-10\"></div>\n          \n          {/* Moving Carousel */}\n          <div className=\"flex animate-scroll\">\n            {duplicatedImages.map((image, index) => (\n              <div\n                key={index}\n                className=\"group flex-shrink-0 mx-4 bg-white rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 p-6 flex items-center justify-center w-48 h-32 border border-gray-100 hover:border-gray-200\"\n              >\n                <Image\n                  src={`/${image}`}\n                  alt={`Client ${(index % 29) + 1}`}\n                  width={120}\n                  height={80}\n                  className=\"object-contain max-w-full max-h-full group-hover:scale-110 transition-transform duration-300\"\n                  style={{ filter: 'grayscale(100%)' }}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.filter = 'grayscale(0%)'\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.filter = 'grayscale(100%)'\n                  }}\n                />\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Additional Info */}\n        <div className=\"text-center mt-12\">\n          <p className=\"text-gray-500 text-sm\">\n            Trusted by 29+ companies and growing\n          </p>\n        </div>\n      </div>\n    </section>\n  )\n}\n\nexport default Client"], "names": [], "mappings": ";;;;;AAGA;AAHA;;;AAKA,MAAM,SAAS;IACb,MAAM,eAAe,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAG,GAAG,CAAC,GAAG,IAAM,AAAC,SAAc,OAAN,IAAI,GAAE;IAEzE,8CAA8C;IAC9C,MAAM,mBAAmB;WAAI;WAAiB;KAAa;IAE3D,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCAGf,6LAAC;4BAAI,WAAU;sCACZ,iBAAiB,GAAG,CAAC,CAAC,OAAO,sBAC5B,6LAAC;oCAEC,WAAU;8CAEV,cAAA,6LAAC,2IAAK;wCACJ,KAAK,AAAC,IAAS,OAAN;wCACT,KAAK,AAAC,UAA0B,OAAjB,AAAC,QAAQ,KAAM;wCAC9B,OAAO;wCACP,QAAQ;wCACR,WAAU;wCACV,OAAO;4CAAE,QAAQ;wCAAkB;wCACnC,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG;wCACjC;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG;wCACjC;;;;;;mCAfG;;;;;;;;;;;;;;;;8BAuBb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;;;;;AAO/C;KA5DM;uCA8DS", "debugId": null}}, {"offset": {"line": 752, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useMemo } from \"react\";\r\n\r\ntype GalleryImage = {\r\n  src: string;\r\n  alt: string;\r\n  width?: number;\r\n  height?: number;\r\n};\r\n\r\n// Curated set of 6 images from the public folder\r\nconst sampleImages: GalleryImage[] = [\r\n  { src: \"/Picture2.jpg\", alt: \"HVAC installation - exterior unit\" },\r\n  { src: \"/Picture4.jpg\", alt: \"Ductwork and air handling system\" },\r\n  { src: \"/Picture5.jpg\", alt: \"Thermostat and control wiring\" },\r\n  { src: \"/Picture6.jpg\", alt: \"Clean indoor unit installation\" },\r\n  { src: \"/Picture8.jpg\", alt: \"Technician onsite maintenance\" },\r\n  { src: \"/Picture11.jpg\", alt: \"Smart climate setup overview\" },\r\n];\r\n\r\nexport default function Gallery() {\r\n  const images = useMemo(() => sampleImages, []);\r\n\r\n  return (\r\n    <section className=\"relative py-12 sm:py-16 bg-white\">\r\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\r\n        <header className=\"relative z-20 mx-auto mb-8 max-w-2xl text-center sm:mb-12\">\r\n          <h2 className=\"text-3xl font-semibold tracking-tight text-black sm:text-4xl\">\r\n            Our Gallery\r\n          </h2>\r\n          <p className=\"mt-3 text-gray-700\">\r\n            Explore our recent HVAC installations, maintenance work, and smart climate solutions.\r\n          </p>\r\n        </header>\r\n\r\n        {/* Clean, consistent 6-image grid (1/2/3 columns responsive) */}\r\n        <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3\">\r\n          {images.map((image, index) => (\r\n            <div\r\n              key={image.src}\r\n              className=\"group w-full transform rounded-2xl bg-white/70 shadow-sm ring-1 ring-black/5 transition duration-300 hover:-translate-y-0.5 hover:shadow-md dark:bg-white/10 dark:ring-white/10\"\r\n            >\r\n              <div className=\"relative aspect-[4/3] overflow-hidden rounded-2xl\">\r\n                <img\r\n                  src={image.src}\r\n                  alt={image.alt}\r\n                  loading=\"lazy\"\r\n                  className=\"h-full w-full object-cover transition duration-300 group-hover:scale-[1.02] group-hover:brightness-[0.95]\"\r\n                />\r\n                <div className=\"pointer-events-none absolute inset-0 bg-gradient-to-t from-black/40 via-black/5 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100\" />\r\n                <div className=\"pointer-events-none absolute inset-x-0 bottom-0 p-3 text-left text-sm font-medium text-white/90\">\r\n                  <span className=\"inline-block translate-y-2 opacity-0 transition-all duration-300 group-hover:translate-y-0 group-hover:opacity-100\">\r\n                    {image.alt}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n\r\n\r\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAWA,iDAAiD;AACjD,MAAM,eAA+B;IACnC;QAAE,KAAK;QAAiB,KAAK;IAAoC;IACjE;QAAE,KAAK;QAAiB,KAAK;IAAmC;IAChE;QAAE,KAAK;QAAiB,KAAK;IAAgC;IAC7D;QAAE,KAAK;QAAiB,KAAK;IAAiC;IAC9D;QAAE,KAAK;QAAiB,KAAK;IAAgC;IAC7D;QAAE,KAAK;QAAkB,KAAK;IAA+B;CAC9D;AAEc,SAAS;;IACtB,MAAM,SAAS,IAAA,wKAAO;mCAAC,IAAM;kCAAc,EAAE;IAE7C,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAO,WAAU;;sCAChB,6LAAC;4BAAG,WAAU;sCAA+D;;;;;;sCAG7E,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;8BAMpC,6LAAC;oBAAI,WAAU;8BACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC;4BAEC,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,KAAK,MAAM,GAAG;wCACd,KAAK,MAAM,GAAG;wCACd,SAAQ;wCACR,WAAU;;;;;;kDAEZ,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDACb,MAAM,GAAG;;;;;;;;;;;;;;;;;2BAbX,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;AAuB5B;GA1CwB;KAAA", "debugId": null}}, {"offset": {"line": 902, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from 'next/link'\r\nimport Image from 'next/image'\r\nimport React from 'react'\r\nimport { motion } from 'framer-motion'\r\n\r\nconst AboutTeaser = () => {\r\n  const features = [\r\n    {\r\n      title: \"Professional Installation\",\r\n      description: \"Expert HVAC installation with precision and quality assurance\",\r\n      icon: \"🔧\"\r\n    },\r\n    {\r\n      title: \"24/7 Maintenance\",\r\n      description: \"Round-the-clock support and maintenance services\",\r\n      icon: \"🛠️\"\r\n    },\r\n    {\r\n      title: \"Energy Efficient\",\r\n      description: \"Eco-friendly solutions that reduce energy consumption\",\r\n      icon: \"🌱\"\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <section className=\"relative py-24 bg-gradient-to-br from-slate-50 via-blue-50/30 to-slate-50 overflow-hidden\">\r\n      {/* Background Pattern */}\r\n      <div className=\"absolute inset-0 opacity-5\">\r\n        <div className=\"absolute inset-0\" style={{\r\n          backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`\r\n        }}></div>\r\n      </div>\r\n\r\n      <div className=\"relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\r\n        {/* Section Header */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.6 }}\r\n          viewport={{ once: true }}\r\n          className=\"text-center mb-20\"\r\n        >\r\n          <h2 className=\"text-4xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight\">\r\n            Leading <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-cyan-600\">HVAC Solutions</span> Provider\r\n          </h2>\r\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\r\n            Delivering professional HVAC and electrical systems with over 8 years of industry expertise,\r\n            ensuring optimal comfort and efficiency for your spaces.\r\n          </p>\r\n        </motion.div>\r\n\r\n        {/* Main Content Grid */}\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20\">\r\n          {/* Left Content - Images */}\r\n          <motion.div\r\n            initial={{ opacity: 0, x: -50 }}\r\n            whileInView={{ opacity: 1, x: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n            className=\"relative\"\r\n          >\r\n            <div className=\"relative h-[550px] rounded-2xl overflow-hidden shadow-2xl group\">\r\n              <Image\r\n                src=\"/about1.jpg\"\r\n                alt=\"Professional HVAC Installation\"\r\n                fill\r\n                className=\"object-cover transition-transform duration-700 group-hover:scale-105\"\r\n                sizes=\"(max-width: 450px) 90vw, 50vw\"\r\n                priority\r\n              />\r\n              {/* Gradient Overlay */}\r\n              <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent\"></div>\r\n\r\n              {/* Experience Badge */}\r\n              <motion.div\r\n                initial={{ opacity: 0, scale: 0.8 }}\r\n                whileInView={{ opacity: 1, scale: 1 }}\r\n                transition={{ duration: 0.6, delay: 0.3 }}\r\n                viewport={{ once: true }}\r\n                className=\"absolute top-6 left-6 bg-white/95 backdrop-blur-md rounded-2xl p-6 shadow-xl border border-white/20\"\r\n              >\r\n                <div className=\"text-4xl font-bold text-blue-600\">8+</div>\r\n                <div className=\"text-sm text-gray-600 font-medium\">Years Experience</div>\r\n              </motion.div>\r\n            </div>\r\n\r\n            {/* Secondary Image */}\r\n            <motion.div\r\n              initial={{ opacity: 0, scale: 0.8 }}\r\n              whileInView={{ opacity: 1, scale: 1 }}\r\n              transition={{ duration: 0.6, delay: 0.5 }}\r\n              viewport={{ once: true }}\r\n              className=\"absolute -bottom-10 -right-10 w-72 h-48 rounded-2xl overflow-hidden shadow-2xl bg-white border-4 border-white group\"\r\n            >\r\n              <Image\r\n                src=\"/about2.jpg\"\r\n                alt=\"HVAC Equipment\"\r\n                fill\r\n                className=\"object-cover transition-transform duration-700 group-hover:scale-105\"\r\n                sizes=\"288px\"\r\n              />\r\n            </motion.div>\r\n          </motion.div>\r\n\r\n          {/* Right Content */}\r\n          <motion.div\r\n            initial={{ opacity: 0, x: 50 }}\r\n            whileInView={{ opacity: 1, x: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n            className=\"space-y-8\"\r\n          >\r\n            <div className=\"space-y-6\">\r\n              <h3 className=\"text-3xl md:text-4xl font-bold text-gray-900 leading-tight\">\r\n                Professional Excellence in Every Project\r\n              </h3>\r\n              <p className=\"text-lg text-gray-600 leading-relaxed\">\r\n                S T HVAC specializes in comprehensive HVAC and LT Electrical solutions,\r\n                providing strategic design, seamless installation, and reliable maintenance\r\n                services across various industries worldwide.\r\n              </p>\r\n            </div>\r\n\r\n            {/* Key Features */}\r\n            <div className=\"grid grid-cols-1 gap-4 mb-8\">\r\n              {features.map((feature, index) => (\r\n                <motion.div\r\n                  key={index}\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  whileInView={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.5, delay: index * 0.1 }}\r\n                  viewport={{ once: true }}\r\n                  className=\"flex items-start gap-4 p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-white/20 hover:bg-white/80 transition-all duration-300\"\r\n                >\r\n                  <div className=\"text-2xl\">{feature.icon}</div>\r\n                  <div>\r\n                    <h4 className=\"text-lg font-semibold text-gray-900 mb-1\">{feature.title}</h4>\r\n                    <p className=\"text-gray-600 text-sm\">{feature.description}</p>\r\n                  </div>\r\n                </motion.div>\r\n              ))}\r\n            </div>\r\n\r\n            {/* CTA Button */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.4 }}\r\n              viewport={{ once: true }}\r\n              className=\"pt-4\"\r\n            >\r\n              <Link\r\n                href=\"/about\"\r\n                className=\"group inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-cyan-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-cyan-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1\"\r\n              >\r\n                Learn More About Us\r\n                <svg className=\"ml-2 w-5 h-5 transition-transform group-hover:translate-x-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\r\n                </svg>\r\n              </Link>\r\n            </motion.div>\r\n          </motion.div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  )\r\n}\r\n\r\nexport default AboutTeaser\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,cAAc;IAClB,MAAM,WAAW;QACf;YACE,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM;QACR;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;oBAAmB,OAAO;wBACvC,iBAAkB;oBACpB;;;;;;;;;;;0BAGF,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,uMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;;oCAAkE;kDACtE,6LAAC;wCAAK,WAAU;kDAA2E;;;;;;oCAAqB;;;;;;;0CAE1H,6LAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;kCAOzE,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,uMAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,2IAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,IAAI;gDACJ,WAAU;gDACV,OAAM;gDACN,QAAQ;;;;;;0DAGV,6LAAC;gDAAI,WAAU;;;;;;0DAGf,6LAAC,uMAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAI;gDAClC,aAAa;oDAAE,SAAS;oDAAG,OAAO;gDAAE;gDACpC,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,UAAU;oDAAE,MAAM;gDAAK;gDACvB,WAAU;;kEAEV,6LAAC;wDAAI,WAAU;kEAAmC;;;;;;kEAClD,6LAAC;wDAAI,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;kDAKvD,6LAAC,uMAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,aAAa;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCACpC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;kDAEV,cAAA,6LAAC,2IAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,IAAI;4CACJ,WAAU;4CACV,OAAM;;;;;;;;;;;;;;;;;0CAMZ,6LAAC,uMAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA6D;;;;;;0DAG3E,6LAAC;gDAAE,WAAU;0DAAwC;;;;;;;;;;;;kDAQvD,6LAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,uMAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,YAAY;oDAAE,UAAU;oDAAK,OAAO,QAAQ;gDAAI;gDAChD,UAAU;oDAAE,MAAM;gDAAK;gDACvB,WAAU;;kEAEV,6LAAC;wDAAI,WAAU;kEAAY,QAAQ,IAAI;;;;;;kEACvC,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAA4C,QAAQ,KAAK;;;;;;0EACvE,6LAAC;gEAAE,WAAU;0EAAyB,QAAQ,WAAW;;;;;;;;;;;;;+CAVtD;;;;;;;;;;kDAiBX,6LAAC,uMAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;kDAEV,cAAA,6LAAC,0KAAI;4CACH,MAAK;4CACL,WAAU;;gDACX;8DAEC,6LAAC;oDAAI,WAAU;oDAA8D,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACrH,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvF;KAjKM;uCAmKS", "debugId": null}}, {"offset": {"line": 1327, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { motion } from 'framer-motion';\r\n\r\nexport function WhyChooseUsSection() {\r\n  const reasons = [\r\n    {\r\n      icon: \"⚡\",\r\n      title: \"Rapid Response\",\r\n      description: \"We understand the importance of breakdown recovery lead time. Our unique internal system ensures we deliver on time, every time.\",\r\n      highlight: \"We Won't Be Beaten\"\r\n    },\r\n    {\r\n      icon: \"🔧\",\r\n      title: \"ST HVAC Method\",\r\n      description: \"Our unique maintenance method is the quickest and safest process around, drastically reducing breakdown time and costs.\",\r\n      highlight: \"Competitive Rates\"\r\n    },\r\n    {\r\n      icon: \"👥\",\r\n      title: \"Expert Personnel\",\r\n      description: \"All locations are staffed by trained and well-equipped personnel. Rapid response and speedy results can be counted on.\",\r\n      highlight: \"Minimized Downtime\"\r\n    },\r\n    {\r\n      icon: \"🎯\",\r\n      title: \"Quality Focus\",\r\n      description: \"No compromise attitude when it comes to quality. We maintain the highest standards in all our service deliveries.\",\r\n      highlight: \"100% Quality Assured\"\r\n    }\r\n  ];\r\n\r\n  const specialties = [\r\n    { icon: \"🏭\", title: \"Industrial HVAC\", description: \"Specialized solutions for manufacturing and industrial facilities\" },\r\n    { icon: \"🏢\", title: \"Commercial Systems\", description: \"Comprehensive HVAC solutions for office buildings and retail spaces\" },\r\n    { icon: \"⚙️\", title: \"Maintenance Contracts\", description: \"Annual maintenance contracts with 24/7 support coverage\" },\r\n    { icon: \"🔌\", title: \"Electrical Services\", description: \"LT electrical installations and maintenance services\" }\r\n  ];\r\n\r\n  return (\r\n    <section className=\"relative w-full py-20 bg-gradient-to-br from-gray-50 via-blue-50/30 to-gray-50 overflow-hidden\">\r\n      {/* Background Elements */}\r\n      <div className=\"absolute inset-0 opacity-5\">\r\n        <div className=\"absolute top-20 left-10 w-72 h-72 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl animate-pulse\"></div>\r\n        <div className=\"absolute bottom-20 right-10 w-72 h-72 bg-cyan-300 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-1000\"></div>\r\n      </div>\r\n\r\n      <div className=\"relative container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl\">\r\n        {/* Section Header */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.6 }}\r\n          viewport={{ once: true }}\r\n          className=\"text-center mb-16\"\r\n        >\r\n          <div className=\"inline-flex items-center gap-2 px-6 py-3 rounded-full bg-gradient-to-r from-blue-100 to-cyan-100 text-blue-800 text-sm font-semibold mb-6\">\r\n            <span className=\"w-2 h-2 bg-blue-600 rounded-full animate-pulse\"></span>\r\n            Why Choose Us\r\n          </div>\r\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\r\n            Your Trusted <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-cyan-600\">HVAC Partner</span>\r\n          </h2>\r\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\r\n            Experience the difference with our proven expertise, rapid response times, and unwavering commitment to quality.\r\n          </p>\r\n        </motion.div>\r\n\r\n        {/* Main Reasons Grid */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 mb-20\">\r\n          {reasons.map((reason, index) => (\r\n            <motion.div\r\n              key={index}\r\n              initial={{ opacity: 0, y: 30 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: index * 0.1 }}\r\n              viewport={{ once: true }}\r\n              className=\"group relative bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 hover:-translate-y-2\"\r\n            >\r\n              <div className=\"flex items-start gap-6\">\r\n                <div className=\"text-4xl group-hover:scale-110 transition-transform duration-300\">\r\n                  {reason.icon}\r\n                </div>\r\n                <div className=\"flex-1\">\r\n                  <h3 className=\"text-2xl font-bold text-gray-900 mb-3\">{reason.title}</h3>\r\n                  <p className=\"text-gray-600 leading-relaxed mb-4\">{reason.description}</p>\r\n                  <div className=\"inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-100 to-cyan-100 text-blue-800 text-sm font-semibold rounded-full\">\r\n                    {reason.highlight}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Specialties Section */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.6 }}\r\n          viewport={{ once: true }}\r\n          className=\"text-center mb-12\"\r\n        >\r\n          <h3 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">Our Specialties</h3>\r\n          <div className=\"w-24 h-1 bg-gradient-to-r from-blue-600 to-cyan-600 mx-auto mb-6\"></div>\r\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\r\n            Comprehensive HVAC and electrical solutions tailored to your specific industry needs.\r\n          </p>\r\n        </motion.div>\r\n\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\">\r\n          {specialties.map((specialty, index) => (\r\n            <motion.div\r\n              key={index}\r\n              initial={{ opacity: 0, scale: 0.8 }}\r\n              whileInView={{ opacity: 1, scale: 1 }}\r\n              transition={{ duration: 0.5, delay: index * 0.1 }}\r\n              viewport={{ once: true }}\r\n              className=\"group text-center p-6 bg-white/60 backdrop-blur-sm rounded-xl border border-white/20 hover:bg-white/80 transition-all duration-300 hover:shadow-lg\"\r\n            >\r\n              <div className=\"text-3xl mb-4 group-hover:scale-110 transition-transform duration-300\">\r\n                {specialty.icon}\r\n              </div>\r\n              <h4 className=\"text-lg font-semibold text-gray-900 mb-2\">{specialty.title}</h4>\r\n              <p className=\"text-gray-600 text-sm\">{specialty.description}</p>\r\n            </motion.div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAGA;AAHA;;;AAKO,SAAS;IACd,MAAM,UAAU;QACd;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,WAAW;QACb;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,WAAW;QACb;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,WAAW;QACb;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,WAAW;QACb;KACD;IAED,MAAM,cAAc;QAClB;YAAE,MAAM;YAAM,OAAO;YAAmB,aAAa;QAAoE;QACzH;YAAE,MAAM;YAAM,OAAO;YAAsB,aAAa;QAAsE;QAC9H;YAAE,MAAM;YAAM,OAAO;YAAyB,aAAa;QAA0D;QACrH;YAAE,MAAM;YAAM,OAAO;YAAuB,aAAa;QAAuD;KACjH;IAED,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,uMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;;;;;;oCAAwD;;;;;;;0CAG1E,6LAAC;gCAAG,WAAU;;oCAAoD;kDACnD,6LAAC;wCAAK,WAAU;kDAA2E;;;;;;;;;;;;0CAE1G,6LAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAMzD,6LAAC;wBAAI,WAAU;kCACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC,uMAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;gCAChD,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,OAAO,IAAI;;;;;;sDAEd,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAyC,OAAO,KAAK;;;;;;8DACnE,6LAAC;oDAAE,WAAU;8DAAsC,OAAO,WAAW;;;;;;8DACrE,6LAAC;oDAAI,WAAU;8DACZ,OAAO,SAAS;;;;;;;;;;;;;;;;;;+BAflB;;;;;;;;;;kCAwBX,6LAAC,uMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAClE,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAKzD,6LAAC;wBAAI,WAAU;kCACZ,YAAY,GAAG,CAAC,CAAC,WAAW,sBAC3B,6LAAC,uMAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;gCAChD,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;kDACZ,UAAU,IAAI;;;;;;kDAEjB,6LAAC;wCAAG,WAAU;kDAA4C,UAAU,KAAK;;;;;;kDACzE,6LAAC;wCAAE,WAAU;kDAAyB,UAAU,WAAW;;;;;;;+BAXtD;;;;;;;;;;;;;;;;;;;;;;AAkBnB;KA/HgB", "debugId": null}}]}