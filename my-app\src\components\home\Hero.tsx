"use client";

import React, { useState, useEffect } from "react";
import { TypewriterEffectSmooth } from "@/components/ui/typewriter-effect-smooth";
import Image from "next/image";

const Hero: React.FC = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  // Enhanced typewriter words with more dynamic content
  const words = [
    { text: "Comfort", className: "text-blue-400 font-bold" },
    { text: "You", className: "text-white font-bold" },
    { text: "Can", className: "text-cyan-400 font-bold" },
    { text: "Trust", className: "text-blue-300 font-bold" },
  ];

  // Background images for slideshow
  const backgroundImages = [
    "/background.jpg",
    "/background1.jpg",
    "/about1.jpg",
  ];

  // Auto-slide effect
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % backgroundImages.length);
    }, 6000);
    return () => clearInterval(interval);
  }, [backgroundImages.length]);

  // Fade in animation on mount
  useEffect(() => {
    setIsVisible(true);
  }, []);

  // Service highlights
  const services = [
    { icon: "❄️", text: "Air Conditioning" },
    { icon: "🔥", text: "Heating Systems" },
    { icon: "🌬️", text: "Air Quality" },
    { icon: "🔧", text: "Maintenance" },
  ];

  return (
    <section
      id="hero"
      className="relative w-full h-screen overflow-hidden flex items-center justify-center"
    >
      {/* Dynamic Background Slideshow */}
      <div className="absolute inset-0">
        {backgroundImages.map((image, index) => (
          <div
            key={index}
            className={`absolute inset-0 transition-opacity duration-1000 ${
              index === currentSlide ? "opacity-100" : "opacity-0"
            }`}
          >
            <Image
              src={image}
              alt={`HVAC Background ${index + 1}`}
              fill
              priority={index === 0}
              className="object-cover"
            />
          </div>
        ))}
        {/* Enhanced overlay with gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900/80 via-slate-900/70 to-cyan-900/80" />

        {/* Animated particles overlay */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-cyan-400 rounded-full animate-pulse" />
          <div className="absolute top-1/3 right-1/3 w-1 h-1 bg-blue-400 rounded-full animate-ping" />
          <div className="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-white rounded-full animate-pulse" />
          <div className="absolute top-2/3 right-1/4 w-1 h-1 bg-cyan-300 rounded-full animate-ping" />
        </div>
      </div>

      {/* Hero Content */}
      <div className={`relative z-10 max-w-6xl mx-auto px-6 sm:px-8 lg:px-12 text-center text-white transition-all duration-1000 ${
        isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-10"
      }`}>

        {/* Company Badge */}
        <div className="inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full border border-white/20 mb-6">
          <span className="text-cyan-400 font-semibold text-sm">🏆 Trusted HVAC Experts Since 2010</span>
        </div>

        {/* Main Heading with Enhanced Typography */}
        <h1 className="text-5xl sm:text-6xl lg:text-7xl xl:text-8xl font-black tracking-tight mb-6">
          <span className="bg-gradient-to-r from-white via-blue-100 to-cyan-200 bg-clip-text text-transparent drop-shadow-2xl">
            ST HVAC
          </span>
          <br />
          <span className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-light text-blue-200">
            SALES & SERVICES
          </span>
        </h1>

        {/* Enhanced Typewriter Effect */}
        <div className="mb-8 flex justify-center">
          <TypewriterEffectSmooth
            words={words}
            className="text-3xl sm:text-4xl lg:text-5xl"
            cursorClassName="bg-cyan-400"
          />
        </div>

        {/* Service Icons Row */}
        <div className="flex justify-center gap-8 mb-8">
          {services.map((service, index) => (
            <div
              key={index}
              className="flex flex-col items-center group cursor-pointer"
            >
              <div className="text-3xl mb-2 group-hover:scale-110 transition-transform duration-300">
                {service.icon}
              </div>
              <span className="text-sm text-blue-200 group-hover:text-white transition-colors duration-300">
                {service.text}
              </span>
            </div>
          ))}
        </div>

        {/* Enhanced Description */}
        <p className="text-xl sm:text-2xl lg:text-3xl text-blue-100 leading-relaxed max-w-4xl mx-auto mb-10">
          Delivering{" "}
          <span className="font-bold text-cyan-300 bg-cyan-300/10 px-2 py-1 rounded">
            premium comfort solutions
          </span>{" "}
          with{" "}
          <span className="font-bold text-blue-300 bg-blue-300/10 px-2 py-1 rounded">
            24/7 expert service
          </span>
        </p>

        {/* Enhanced CTA Buttons */}
        <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
          <a
            href="tel:+15551234567"
            className="group relative px-10 py-5 bg-gradient-to-r from-cyan-500 to-blue-600 text-white rounded-2xl font-bold text-xl shadow-2xl hover:shadow-cyan-500/25 transition-all duration-300 transform hover:-translate-y-2 hover:scale-105"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-2xl blur opacity-0 group-hover:opacity-50 transition-opacity duration-300" />
            <span className="relative flex items-center justify-center gap-3">
              <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
              </svg>
              Call Now: Emergency Service
            </span>
          </a>

          <a
            href="/contact"
            className="group px-10 py-5 border-3 border-cyan-400 text-cyan-400 rounded-2xl font-bold text-xl hover:bg-cyan-400 hover:text-slate-900 transition-all duration-300 transform hover:-translate-y-2 hover:scale-105 shadow-lg hover:shadow-2xl backdrop-blur-sm bg-white/5"
          >
            <span className="flex items-center justify-center gap-3">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Free Quote in 24hrs
            </span>
          </a>
        </div>

        {/* Trust Indicators */}
        <div className="mt-12 flex flex-wrap justify-center gap-8 text-blue-200">
          <div className="flex items-center gap-2">
            <span className="text-yellow-400">⭐⭐⭐⭐⭐</span>
            <span className="text-sm">500+ Happy Customers</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-green-400">✓</span>
            <span className="text-sm">Licensed & Insured</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-cyan-400">🕐</span>
            <span className="text-sm">24/7 Emergency Service</span>
          </div>
        </div>
      </div>

      {/* Slide Indicators */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex gap-3 z-20">
        {backgroundImages.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentSlide(index)}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentSlide
                ? "bg-cyan-400 scale-125"
                : "bg-white/50 hover:bg-white/75"
            }`}
          />
        ))}
      </div>
    </section>
  );
};

export default Hero;
