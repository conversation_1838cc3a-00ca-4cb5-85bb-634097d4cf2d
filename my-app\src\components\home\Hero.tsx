"use client";

import React from "react";
import { TypewriterEffectSmooth } from "@/components/ui/typewriter-effect-smooth";
import Image from "next/image";

const Hero: React.FC = () => {
  const words = [
    { text: "Breathe", className: "text-blue-400 font-semibold italic" },
    { text: "Easy", className: "text-cyan-400 font-semibold italic" },
    { text: "...", className: "text-blue-400 font-semibold italic" },
    { text: "Live", className: "text-cyan-400 font-semibold italic" },
    { text: "Easy", className: "text-blue-400 font-semibold italic" },
    { text: "...", className: "text-cyan-400 font-semibold italic" },
  ];

  return (
    <section
      id="hero"
      className="relative w-full h-screen overflow-hidden flex items-center justify-center"
    >
      {/* 🔹 Background with overlay */}
      <div className="absolute inset-0">
        <Image
          src="/background.jpg"
          alt="Modern HVAC System Components"
          fill
          priority
          className="object-cover opacity-95 animate-zoomInOut"
        />
      </div>

      {/* 🔹 Hero Content */}
      <div className="relative z-10 max-w-5xl mx-auto px-6 sm:px-8 lg:px-12 text-center text-white pt-32 sm:pt-40 lg:pt-44 pb-20">
        {/* Heading */}
        <h1 className="text-4xl sm:text-5xl lg:text-6xl font-extrabold tracking-tight drop-shadow-lg">
          S T HVAC SALES & SERVICES
        </h1>

        {/* Typewriter */}
        <div className="mt-6 flex justify-center">
          <TypewriterEffectSmooth
            words={words}
            className="text-2xl sm:text-3xl lg:text-4xl"
            cursorClassName="bg-cyan-400"
          />
        </div>

        {/* Description */}
        <p className="mt-6 text-lg sm:text-xl lg:text-2xl text-blue-100 leading-relaxed max-w-3xl mx-auto">
          Leading the way in professional heating, cooling, and air quality.{" "}
          Delivering{" "}
          <span className="font-bold text-cyan-300">innovative solutions</span>{" "}
          and <span className="font-bold text-blue-300">unmatched service</span>{" "}
          for your ultimate comfort.
        </p>

        {/* CTA Buttons */}
        <div className="mt-10 flex flex-col sm:flex-row gap-6 justify-center">
          <a
            href="tel:+15551234567"
            className="group relative px-8 py-4 bg-gradient-to-r from-blue-600 to-cyan-600 text-white rounded-full font-bold text-lg shadow-lg hover:from-blue-700 hover:to-cyan-700 transition-all duration-300 transform hover:-translate-y-1"
          >
            <span className="flex items-center justify-center gap-2">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
              </svg>
              Request a Call
            </span>
          </a>
          <a
            href="/contact"
            className="px-8 py-4 border-2 border-cyan-400 text-cyan-400 rounded-full font-bold text-lg hover:bg-cyan-400 hover:text-slate-900 transition-all duration-300 transform hover:-translate-y-1 shadow-lg hover:shadow-xl"
          >
            Get a Free Quote
          </a>
        </div>
      </div>
    </section>
  );
};

export default Hero;
