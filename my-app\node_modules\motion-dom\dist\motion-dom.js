!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("motion-utils")):"function"==typeof define&&define.amd?define(["exports","motion-utils"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).MotionDom={},t.MotionUtils)}(this,function(t,e){"use strict";const n=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],i={value:null,addProjectionMetrics:null};function s(t,s){let r=!1,a=!0;const o={delta:0,timestamp:0,isProcessing:!1},l=()=>r=!0,u=n.reduce((t,e)=>(t[e]=function(t,e){let n=new Set,s=new Set,r=!1,a=!1;const o=new WeakSet;let l={delta:0,timestamp:0,isProcessing:!1},u=0;function c(e){o.has(e)&&(h.schedule(e),t()),u++,e(l)}const h={schedule:(t,e=!1,i=!1)=>{const a=i&&r?n:s;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{s.delete(t),o.delete(t)},process:t=>{l=t,r?a=!0:(r=!0,[n,s]=[s,n],n.forEach(c),e&&i.value&&i.value.frameloop[e].push(u),u=0,n.clear(),r=!1,a&&(a=!1,h.process(t)))}};return h}(l,s?e:void 0),t),{}),{setup:c,read:h,resolveKeyframes:d,preUpdate:m,update:p,preRender:f,render:g,postRender:y}=u,v=()=>{const n=e.MotionGlobalConfig.useManualTiming?o.timestamp:performance.now();r=!1,e.MotionGlobalConfig.useManualTiming||(o.delta=a?1e3/60:Math.max(Math.min(n-o.timestamp,40),1)),o.timestamp=n,o.isProcessing=!0,c.process(o),h.process(o),d.process(o),m.process(o),p.process(o),f.process(o),g.process(o),y.process(o),o.isProcessing=!1,r&&s&&(a=!1,t(v))};return{schedule:n.reduce((e,n)=>{const i=u[n];return e[n]=(e,n=!1,s=!1)=>(r||(r=!0,a=!0,o.isProcessing||t(v)),i.schedule(e,n,s)),e},{}),cancel:t=>{for(let e=0;e<n.length;e++)u[n[e]].cancel(t)},state:o,steps:u}}const{schedule:r,cancel:a,state:o,steps:l}=s("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:e.noop,!0);let u;function c(){u=void 0}const h={now:()=>(void 0===u&&h.set(o.isProcessing||e.MotionGlobalConfig.useManualTiming?o.timestamp:performance.now()),u),set:t=>{u=t,queueMicrotask(c)}},d={layout:0,mainThread:0,waapi:0},m=t=>e=>"string"==typeof e&&e.startsWith(t),p=m("--"),f=m("var(--"),g=t=>!!f(t)&&y.test(t.split("/*")[0].trim()),y=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,v={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},T={...v,transform:t=>e.clamp(0,1,t)},b={...v,default:1},w=t=>Math.round(1e5*t)/1e5,M=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const x=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,S=(t,e)=>n=>Boolean("string"==typeof n&&x.test(n)&&n.startsWith(t)||e&&!function(t){return null==t}(n)&&Object.prototype.hasOwnProperty.call(n,e)),A=(t,e,n)=>i=>{if("string"!=typeof i)return i;const[s,r,a,o]=i.match(M);return{[t]:parseFloat(s),[e]:parseFloat(r),[n]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},k={...v,transform:t=>Math.round((t=>e.clamp(0,255,t))(t))},E={test:S("rgb","red"),parse:A("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:i=1})=>"rgba("+k.transform(t)+", "+k.transform(e)+", "+k.transform(n)+", "+w(T.transform(i))+")"};const V={test:S("#"),parse:function(t){let e="",n="",i="",s="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),i=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),i=t.substring(3,4),s=t.substring(4,5),e+=e,n+=n,i+=i,s+=s),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(i,16),alpha:s?parseInt(s,16)/255:1}},transform:E.transform},P=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),D=P("deg"),R=P("%"),F=P("px"),O=P("vh"),C=P("vw"),K=(()=>({...R,parse:t=>R.parse(t)/100,transform:t=>R.transform(100*t)}))(),L={test:S("hsl","hue"),parse:A("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:i=1})=>"hsla("+Math.round(t)+", "+R.transform(w(e))+", "+R.transform(w(n))+", "+w(T.transform(i))+")"},W={test:t=>E.test(t)||V.test(t)||L.test(t),parse:t=>E.test(t)?E.parse(t):L.test(t)?L.parse(t):V.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?E.transform(t):L.transform(t),getAnimatableNone:t=>{const e=W.parse(t);return e.alpha=0,W.transform(e)}},B=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const $="number",N="color",j=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function I(t){const e=t.toString(),n=[],i={color:[],number:[],var:[]},s=[];let r=0;const a=e.replace(j,t=>(W.test(t)?(i.color.push(r),s.push(N),n.push(W.parse(t))):t.startsWith("var(")?(i.var.push(r),s.push("var"),n.push(t)):(i.number.push(r),s.push($),n.push(parseFloat(t))),++r,"${}")).split("${}");return{values:n,split:a,indexes:i,types:s}}function Y(t){return I(t).values}function z(t){const{split:e,types:n}=I(t),i=e.length;return t=>{let s="";for(let r=0;r<i;r++)if(s+=e[r],void 0!==t[r]){const e=n[r];s+=e===$?w(t[r]):e===N?W.transform(t[r]):t[r]}return s}}const X=t=>"number"==typeof t?0:W.test(t)?W.getAnimatableNone(t):t;const G={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(M)?.length||0)+(t.match(B)?.length||0)>0},parse:Y,createTransformer:z,getAnimatableNone:function(t){const e=Y(t);return z(t)(e.map(X))}};function U(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function q({hue:t,saturation:e,lightness:n,alpha:i}){t/=360,n/=100;let s=0,r=0,a=0;if(e/=100){const i=n<.5?n*(1+e):n+e-n*e,o=2*n-i;s=U(o,i,t+1/3),r=U(o,i,t),a=U(o,i,t-1/3)}else s=r=a=n;return{red:Math.round(255*s),green:Math.round(255*r),blue:Math.round(255*a),alpha:i}}function Z(t,e){return n=>n>0?e:t}const _=(t,e,n)=>t+(e-t)*n,H=(t,e,n)=>{const i=t*t,s=n*(e*e-i)+i;return s<0?0:Math.sqrt(s)},J=[V,E,L];function Q(t){const n=(i=t,J.find(t=>t.test(i)));var i;if(e.warning(Boolean(n),`'${t}' is not an animatable color. Use the equivalent color code instead.`,"color-not-animatable"),!Boolean(n))return!1;let s=n.parse(t);return n===L&&(s=q(s)),s}const tt=(t,e)=>{const n=Q(t),i=Q(e);if(!n||!i)return Z(t,e);const s={...n};return t=>(s.red=H(n.red,i.red,t),s.green=H(n.green,i.green,t),s.blue=H(n.blue,i.blue,t),s.alpha=_(n.alpha,i.alpha,t),E.transform(s))},et=new Set(["none","hidden"]);function nt(t,e){return et.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}function it(t,e){return n=>_(t,e,n)}function st(t){return"number"==typeof t?it:"string"==typeof t?g(t)?Z:W.test(t)?tt:ot:Array.isArray(t)?rt:"object"==typeof t?W.test(t)?tt:at:Z}function rt(t,e){const n=[...t],i=n.length,s=t.map((t,n)=>st(t)(t,e[n]));return t=>{for(let e=0;e<i;e++)n[e]=s[e](t);return n}}function at(t,e){const n={...t,...e},i={};for(const s in n)void 0!==t[s]&&void 0!==e[s]&&(i[s]=st(t[s])(t[s],e[s]));return t=>{for(const e in i)n[e]=i[e](t);return n}}const ot=(t,n)=>{const i=G.createTransformer(n),s=I(t),r=I(n);return s.indexes.var.length===r.indexes.var.length&&s.indexes.color.length===r.indexes.color.length&&s.indexes.number.length>=r.indexes.number.length?et.has(t)&&!r.values.length||et.has(n)&&!s.values.length?nt(t,n):e.pipe(rt(function(t,e){const n=[],i={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){const r=e.types[s],a=t.indexes[r][i[r]],o=t.values[a]??0;n[s]=o,i[r]++}return n}(s,r),r.values),i):(e.warning(!0,`Complex values '${t}' and '${n}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`,"complex-values-different"),Z(t,n))};function lt(t,e,n){if("number"==typeof t&&"number"==typeof e&&"number"==typeof n)return _(t,e,n);return st(t)(t,e)}const ut=t=>{const e=({timestamp:e})=>t(e);return{start:(t=!0)=>r.update(e,t),stop:()=>a(e),now:()=>o.isProcessing?o.timestamp:h.now()}},ct=(t,e,n=10)=>{let i="";const s=Math.max(Math.round(e/n),2);for(let e=0;e<s;e++)i+=Math.round(1e4*t(e/(s-1)))/1e4+", ";return`linear(${i.substring(0,i.length-2)})`},ht=2e4;function dt(t){let e=0;let n=t.next(e);for(;!n.done&&e<ht;)e+=50,n=t.next(e);return e>=ht?1/0:e}function mt(t,n=100,i){const s=i({...t,keyframes:[0,n]}),r=Math.min(dt(s),ht);return{type:"keyframes",ease:t=>s.next(r*t).value/n,duration:e.millisecondsToSeconds(r)}}function pt(t,n,i){const s=Math.max(n-5,0);return e.velocityPerSecond(i-t(s),n-s)}const ft=100,gt=10,yt=1,vt=0,Tt=800,bt=.3,wt=.3,Mt={granular:.01,default:2},xt={granular:.005,default:.5},St=.01,At=10,kt=.05,Et=1,Vt=.001;function Pt({duration:t=Tt,bounce:n=bt,velocity:i=vt,mass:s=yt}){let r,a;e.warning(t<=e.secondsToMilliseconds(At),"Spring duration must be 10 seconds or less","spring-duration-limit");let o=1-n;o=e.clamp(kt,Et,o),t=e.clamp(St,At,e.millisecondsToSeconds(t)),o<1?(r=e=>{const n=e*o,s=n*t,r=n-i,a=Rt(e,o),l=Math.exp(-s);return Vt-r/a*l},a=e=>{const n=e*o*t,s=n*i+i,a=Math.pow(o,2)*Math.pow(e,2)*t,l=Math.exp(-n),u=Rt(Math.pow(e,2),o);return(-r(e)+Vt>0?-1:1)*((s-a)*l)/u}):(r=e=>Math.exp(-e*t)*((e-i)*t+1)-.001,a=e=>Math.exp(-e*t)*(t*t*(i-e)));const l=function(t,e,n){let i=n;for(let n=1;n<Dt;n++)i-=t(i)/e(i);return i}(r,a,5/t);if(t=e.secondsToMilliseconds(t),isNaN(l))return{stiffness:ft,damping:gt,duration:t};{const e=Math.pow(l,2)*s;return{stiffness:e,damping:2*o*Math.sqrt(s*e),duration:t}}}const Dt=12;function Rt(t,e){return t*Math.sqrt(1-e*e)}const Ft=["duration","bounce"],Ot=["stiffness","damping","mass"];function Ct(t,e){return e.some(e=>void 0!==t[e])}function Kt(t=wt,n=bt){const i="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:n}:t;let{restSpeed:s,restDelta:r}=i;const a=i.keyframes[0],o=i.keyframes[i.keyframes.length-1],l={done:!1,value:a},{stiffness:u,damping:c,mass:h,duration:d,velocity:m,isResolvedFromDuration:p}=function(t){let n={velocity:vt,stiffness:ft,damping:gt,mass:yt,isResolvedFromDuration:!1,...t};if(!Ct(t,Ot)&&Ct(t,Ft))if(t.visualDuration){const i=t.visualDuration,s=2*Math.PI/(1.2*i),r=s*s,a=2*e.clamp(.05,1,1-(t.bounce||0))*Math.sqrt(r);n={...n,mass:yt,stiffness:r,damping:a}}else{const e=Pt(t);n={...n,...e,mass:yt},n.isResolvedFromDuration=!0}return n}({...i,velocity:-e.millisecondsToSeconds(i.velocity||0)}),f=m||0,g=c/(2*Math.sqrt(u*h)),y=o-a,v=e.millisecondsToSeconds(Math.sqrt(u/h)),T=Math.abs(y)<5;let b;if(s||(s=T?Mt.granular:Mt.default),r||(r=T?xt.granular:xt.default),g<1){const t=Rt(v,g);b=e=>{const n=Math.exp(-g*v*e);return o-n*((f+g*v*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}}else if(1===g)b=t=>o-Math.exp(-v*t)*(y+(f+v*y)*t);else{const t=v*Math.sqrt(g*g-1);b=e=>{const n=Math.exp(-g*v*e),i=Math.min(t*e,300);return o-n*((f+g*v*y)*Math.sinh(i)+t*y*Math.cosh(i))/t}}const w={calculatedDuration:p&&d||null,next:t=>{const n=b(t);if(p)l.done=t>=d;else{let i=0===t?f:0;g<1&&(i=0===t?e.secondsToMilliseconds(f):pt(b,t,n));const a=Math.abs(i)<=s,u=Math.abs(o-n)<=r;l.done=a&&u}return l.value=l.done?o:n,l},toString:()=>{const t=Math.min(dt(w),ht),e=ct(e=>w.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return w}function Lt({keyframes:t,velocity:e=0,power:n=.8,timeConstant:i=325,bounceDamping:s=10,bounceStiffness:r=500,modifyTarget:a,min:o,max:l,restDelta:u=.5,restSpeed:c}){const h=t[0],d={done:!1,value:h},m=t=>void 0===o?l:void 0===l||Math.abs(o-t)<Math.abs(l-t)?o:l;let p=n*e;const f=h+p,g=void 0===a?f:a(f);g!==f&&(p=g-h);const y=t=>-p*Math.exp(-t/i),v=t=>g+y(t),T=t=>{const e=y(t),n=v(t);d.done=Math.abs(e)<=u,d.value=d.done?g:n};let b,w;const M=t=>{var e;(e=d.value,void 0!==o&&e<o||void 0!==l&&e>l)&&(b=t,w=Kt({keyframes:[d.value,m(d.value)],velocity:pt(v,t,d.value),damping:s,stiffness:r,restDelta:u,restSpeed:c}))};return M(0),{calculatedDuration:null,next:t=>{let e=!1;return w||void 0!==b||(e=!0,T(t),M(t)),void 0!==b&&t>=b?w.next(t-b):(!e&&T(t),d)}}}function Wt(t,n,{clamp:i=!0,ease:s,mixer:r}={}){const a=t.length;if(e.invariant(a===n.length,"Both input and output ranges must be the same length","range-length"),1===a)return()=>n[0];if(2===a&&n[0]===n[1])return()=>n[1];const o=t[0]===t[1];t[0]>t[a-1]&&(t=[...t].reverse(),n=[...n].reverse());const l=function(t,n,i){const s=[],r=i||e.MotionGlobalConfig.mix||lt,a=t.length-1;for(let i=0;i<a;i++){let a=r(t[i],t[i+1]);if(n){const t=Array.isArray(n)?n[i]||e.noop:n;a=e.pipe(t,a)}s.push(a)}return s}(n,s,r),u=l.length,c=i=>{if(o&&i<t[0])return n[0];let s=0;if(u>1)for(;s<t.length-2&&!(i<t[s+1]);s++);const r=e.progress(t[s],t[s+1],i);return l[s](r)};return i?n=>c(e.clamp(t[0],t[a-1],n)):c}function Bt(t,n){const i=t[t.length-1];for(let s=1;s<=n;s++){const r=e.progress(0,n,s);t.push(_(i,1,r))}}function $t(t){const e=[0];return Bt(e,t.length-1),e}function Nt(t,e){return t.map(t=>t*e)}function jt(t,n){return t.map(()=>n||e.easeInOut).splice(0,t.length-1)}function It({duration:t=300,keyframes:n,times:i,ease:s="easeInOut"}){const r=e.isEasingArray(s)?s.map(e.easingDefinitionToFunction):e.easingDefinitionToFunction(s),a={done:!1,value:n[0]},o=Wt(Nt(i&&i.length===n.length?i:$t(n),t),n,{ease:Array.isArray(r)?r:jt(n,r)});return{calculatedDuration:t,next:e=>(a.value=o(e),a.done=e>=t,a)}}Kt.applyToOptions=t=>{const n=mt(t,100,Kt);return t.ease=n.ease,t.duration=e.secondsToMilliseconds(n.duration),t.type="keyframes",t};const Yt=t=>null!==t;function zt(t,{repeat:e,repeatType:n="loop"},i,s=1){const r=t.filter(Yt),a=s<0||e&&"loop"!==n&&e%2==1?0:r.length-1;return a&&void 0!==i?i:r[a]}const Xt={decay:Lt,inertia:Lt,tween:It,keyframes:It,spring:Kt};function Gt(t){"string"==typeof t.type&&(t.type=Xt[t.type])}class Ut{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}const qt=t=>t/100;class Zt extends Ut{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{const{motionValue:t}=this.options;t&&t.updatedAt!==h.now()&&this.tick(h.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},d.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){const{options:t}=this;Gt(t);const{type:n=It,repeat:i=0,repeatDelay:s=0,repeatType:r,velocity:a=0}=t;let{keyframes:o}=t;const l=n||It;l!==It&&"number"!=typeof o[0]&&(this.mixKeyframes=e.pipe(qt,lt(o[0],o[1])),o=[0,100]);const u=l({...t,keyframes:o});"mirror"===r&&(this.mirroredGenerator=l({...t,keyframes:[...o].reverse(),velocity:-a})),null===u.calculatedDuration&&(u.calculatedDuration=dt(u));const{calculatedDuration:c}=u;this.calculatedDuration=c,this.resolvedDuration=c+s,this.totalDuration=this.resolvedDuration*(i+1)-s,this.generator=u}updateTime(t){const e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,n=!1){const{generator:i,totalDuration:s,mixKeyframes:r,mirroredGenerator:a,resolvedDuration:o,calculatedDuration:l}=this;if(null===this.startTime)return i.next(0);const{delay:u=0,keyframes:c,repeat:h,repeatType:d,repeatDelay:m,type:p,onUpdate:f,finalKeyframe:g}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-s/this.speed,this.startTime)),n?this.currentTime=t:this.updateTime(t);const y=this.currentTime-u*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?y<0:y>s;this.currentTime=Math.max(y,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=s);let T=this.currentTime,b=i;if(h){const t=Math.min(this.currentTime,s)/o;let n=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&n--,n=Math.min(n,h+1);Boolean(n%2)&&("reverse"===d?(i=1-i,m&&(i-=m/o)):"mirror"===d&&(b=a)),T=e.clamp(0,1,i)*o}const w=v?{done:!1,value:c[0]}:b.next(T);r&&(w.value=r(w.value));let{done:M}=w;v||null===l||(M=this.playbackSpeed>=0?this.currentTime>=s:this.currentTime<=0);const x=null===this.holdTime&&("finished"===this.state||"running"===this.state&&M);return x&&p!==Lt&&(w.value=zt(c,this.options,g,this.speed)),f&&f(w.value),x&&this.finish(),w}then(t,e){return this.finished.then(t,e)}get duration(){return e.millisecondsToSeconds(this.calculatedDuration)}get iterationDuration(){const{delay:t=0}=this.options||{};return this.duration+e.millisecondsToSeconds(t)}get time(){return e.millisecondsToSeconds(this.currentTime)}set time(t){t=e.secondsToMilliseconds(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(h.now());const n=this.playbackSpeed!==t;this.playbackSpeed=t,n&&(this.time=e.millisecondsToSeconds(this.currentTime))}play(){if(this.isStopped)return;const{driver:t=ut,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();const n=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=n):null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime||(this.startTime=e??n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(h.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,d.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}function _t(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}const Ht=t=>180*t/Math.PI,Jt=t=>{const e=Ht(Math.atan2(t[1],t[0]));return te(e)},Qt={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:Jt,rotateZ:Jt,skewX:t=>Ht(Math.atan(t[1])),skewY:t=>Ht(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},te=t=>((t%=360)<0&&(t+=360),t),ee=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),ne=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),ie={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ee,scaleY:ne,scale:t=>(ee(t)+ne(t))/2,rotateX:t=>te(Ht(Math.atan2(t[6],t[5]))),rotateY:t=>te(Ht(Math.atan2(-t[2],t[0]))),rotateZ:Jt,rotate:Jt,skewX:t=>Ht(Math.atan(t[4])),skewY:t=>Ht(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function se(t){return t.includes("scale")?1:0}function re(t,e){if(!t||"none"===t)return se(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let i,s;if(n)i=ie,s=n;else{const e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=Qt,s=e}if(!s)return se(e);const r=i[e],a=s[1].split(",").map(ae);return"function"==typeof r?r(a):a[r]}function ae(t){return parseFloat(t.trim())}const oe=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],le=(()=>new Set(oe))(),ue=t=>t===v||t===F,ce=new Set(["x","y","z"]),he=oe.filter(t=>!ce.has(t));const de={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>re(e,"x"),y:(t,{transform:e})=>re(e,"y")};de.translateX=de.x,de.translateY=de.y;const me=new Set;let pe=!1,fe=!1,ge=!1;function ye(){if(fe){const t=Array.from(me).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),n=new Map;e.forEach(t=>{const e=function(t){const e=[];return he.forEach(n=>{const i=t.getValue(n);void 0!==i&&(e.push([n,i.get()]),i.set(n.startsWith("scale")?1:0))}),e}(t);e.length&&(n.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();const e=n.get(t);e&&e.forEach(([e,n])=>{t.getValue(e)?.set(n)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}fe=!1,pe=!1,me.forEach(t=>t.complete(ge)),me.clear()}function ve(){me.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(fe=!0)})}function Te(){ge=!0,ve(),ye(),ge=!1}class be{constructor(t,e,n,i,s,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=n,this.motionValue=i,this.element=s,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(me.add(this),pe||(pe=!0,r.read(ve),r.resolveKeyframes(ye))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:n,motionValue:i}=this;if(null===t[0]){const s=i?.get(),r=t[t.length-1];if(void 0!==s)t[0]=s;else if(n&&e){const i=n.readValue(e,r);null!=i&&(t[0]=i)}void 0===t[0]&&(t[0]=r),i&&void 0===s&&i.set(t[0])}_t(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),me.delete(this)}cancel(){"scheduled"===this.state&&(me.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}const we=t=>t.startsWith("--");function Me(t,e,n){we(e)?t.style.setProperty(e,n):t.style[e]=n}const xe=e.memo(()=>void 0!==window.ScrollTimeline),Se={};function Ae(t,n){const i=e.memo(t);return()=>Se[n]??i()}const ke=Ae(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),Ee=([t,e,n,i])=>`cubic-bezier(${t}, ${e}, ${n}, ${i})`,Ve={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Ee([0,.65,.55,1]),circOut:Ee([.55,0,1,.45]),backIn:Ee([.31,.01,.66,-.59]),backOut:Ee([.33,1.53,.69,.99])};function Pe(t,n){return t?"function"==typeof t?ke()?ct(t,n):"ease-out":e.isBezierDefinition(t)?Ee(t):Array.isArray(t)?t.map(t=>Pe(t,n)||Ve.easeOut):Ve[t]:void 0}function De(t,e,n,{delay:s=0,duration:r=300,repeat:a=0,repeatType:o="loop",ease:l="easeOut",times:u}={},c=void 0){const h={[e]:n};u&&(h.offset=u);const m=Pe(l,r);Array.isArray(m)&&(h.easing=m),i.value&&d.waapi++;const p={delay:s,duration:r,easing:Array.isArray(m)?"linear":m,fill:"both",iterations:a+1,direction:"reverse"===o?"alternate":"normal"};c&&(p.pseudoElement=c);const f=t.animate(h,p);return i.value&&f.finished.finally(()=>{d.waapi--}),f}function Re(t){return"function"==typeof t&&"applyToOptions"in t}function Fe({type:t,...e}){return Re(t)&&ke()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}class Oe extends Ut{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:n,name:i,keyframes:s,pseudoElement:r,allowFlatten:a=!1,finalKeyframe:o,onComplete:l}=t;this.isPseudoElement=Boolean(r),this.allowFlatten=a,this.options=t,e.invariant("string"!=typeof t.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");const u=Fe(t);this.animation=De(n,i,s,u,r),!1===u.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){const t=zt(s,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):Me(n,i,t),this.animation.cancel()}l?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const t=this.animation.effect?.getComputedTiming?.().duration||0;return e.millisecondsToSeconds(Number(t))}get iterationDuration(){const{delay:t=0}=this.options||{};return this.duration+e.millisecondsToSeconds(t)}get time(){return e.millisecondsToSeconds(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=e.secondsToMilliseconds(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:n}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&xe()?(this.animation.timeline=t,e.noop):n(this)}}const Ce={anticipate:e.anticipate,backInOut:e.backInOut,circInOut:e.circInOut};function Ke(t){"string"==typeof t.ease&&t.ease in Ce&&(t.ease=Ce[t.ease])}class Le extends Oe{constructor(t){Ke(t),Gt(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:n,onUpdate:i,onComplete:s,element:r,...a}=this.options;if(!n)return;if(void 0!==t)return void n.set(t);const o=new Zt({...a,autoplay:!1}),l=e.secondsToMilliseconds(this.finishedTime??this.time);n.setWithVelocity(o.sample(l-10).value,o.sample(l).value,10),o.stop()}}const We=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!G.test(t)&&"0"!==t||t.startsWith("url(")));function Be(t){t.duration=0,t.type="keyframes"}const $e=new Set(["opacity","clipPath","filter","transform"]),Ne=e.memo(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function je(t){const{motionValue:e,name:n,repeatDelay:i,repeatType:s,damping:r,type:a}=t,o=e?.owner?.current;if(!(o instanceof HTMLElement))return!1;const{onUpdate:l,transformTemplate:u}=e.owner.getProps();return Ne()&&n&&$e.has(n)&&("transform"!==n||!u)&&!l&&!i&&"mirror"!==s&&0!==r&&"inertia"!==a}class Ie{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>t.finished))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let n=0;n<this.animations.length;n++)this.animations[n][t]=e}attachTimeline(t){const e=this.animations.map(e=>e.attachTimeline(t));return()=>{e.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get state(){return this.getAll("state")}get startTime(){return this.getAll("startTime")}get duration(){return Ye(this.animations,"duration")}get iterationDuration(){return Ye(this.animations,"iterationDuration")}runAll(t){this.animations.forEach(e=>e[t]())}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}function Ye(t,e){let n=0;for(let i=0;i<t.length;i++){const s=t[i][e];null!==s&&s>n&&(n=s)}return n}class ze extends Oe{constructor(t){super(),this.animation=t,t.onfinish=()=>{this.finishedTime=this.time,this.notifyFinished()}}}const Xe=new WeakMap;const Ge=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Ue(t){const e=Ge.exec(t);if(!e)return[,];const[,n,i,s]=e;return[`--${n??i}`,s]}function qe(t,n,i=1){e.invariant(i<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`,"max-css-var-depth");const[s,r]=Ue(t);if(!s)return;const a=window.getComputedStyle(n).getPropertyValue(s);if(a){const t=a.trim();return e.isNumericalString(t)?parseFloat(t):t}return g(r)?qe(r,n,i+1):r}function Ze(t,e){return t?.[e]??t?.default??t}const _e=new Set(["width","height","top","left","right","bottom",...oe]),He=t=>e=>e.test(t),Je=[v,F,R,D,C,O,{test:t=>"auto"===t,parse:t=>t}],Qe=t=>Je.find(He(t));function tn(t){return"number"==typeof t?0===t:null===t||("none"===t||"0"===t||e.isZeroValueString(t))}const en=new Set(["brightness","contrast","saturate","opacity"]);function nn(t){const[e,n]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[i]=n.match(M)||[];if(!i)return t;const s=n.replace(i,"");let r=en.has(e)?1:0;return i!==n&&(r*=100),e+"("+r+s+")"}const sn=/\b([a-z-]*)\(.*?\)/gu,rn={...G,getAnimatableNone:t=>{const e=t.match(sn);return e?e.map(nn).join(" "):t}},an={...v,transform:Math.round},on={rotate:D,rotateX:D,rotateY:D,rotateZ:D,scale:b,scaleX:b,scaleY:b,scaleZ:b,skew:D,skewX:D,skewY:D,distance:F,translateX:F,translateY:F,translateZ:F,x:F,y:F,z:F,perspective:F,transformPerspective:F,opacity:T,originX:K,originY:K,originZ:F},ln={borderWidth:F,borderTopWidth:F,borderRightWidth:F,borderBottomWidth:F,borderLeftWidth:F,borderRadius:F,radius:F,borderTopLeftRadius:F,borderTopRightRadius:F,borderBottomRightRadius:F,borderBottomLeftRadius:F,width:F,maxWidth:F,height:F,maxHeight:F,top:F,right:F,bottom:F,left:F,padding:F,paddingTop:F,paddingRight:F,paddingBottom:F,paddingLeft:F,margin:F,marginTop:F,marginRight:F,marginBottom:F,marginLeft:F,backgroundPositionX:F,backgroundPositionY:F,...on,zIndex:an,fillOpacity:T,strokeOpacity:T,numOctaves:an},un={...ln,color:W,backgroundColor:W,outlineColor:W,fill:W,stroke:W,borderColor:W,borderTopColor:W,borderRightColor:W,borderBottomColor:W,borderLeftColor:W,filter:rn,WebkitFilter:rn},cn=t=>un[t];function hn(t,e){let n=cn(t);return n!==rn&&(n=G),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const dn=new Set(["auto","none","0"]);const mn=new Set(["borderWidth","borderTopWidth","borderRightWidth","borderBottomWidth","borderLeftWidth","borderRadius","radius","borderTopLeftRadius","borderTopRightRadius","borderBottomRightRadius","borderBottomLeftRadius","width","maxWidth","height","maxHeight","top","right","bottom","left","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","margin","marginTop","marginRight","marginBottom","marginLeft","backgroundPositionX","backgroundPositionY"]);const pn=e.memo(()=>{try{document.createElement("div").animate({opacity:[1]})}catch(t){return!1}return!0}),fn=new Set(["opacity","clipPath","filter","transform"]);function gn(t,e,n){if(t instanceof EventTarget)return[t];if("string"==typeof t){let i=document;e&&(i=e.current);const s=n?.[t]??i.querySelectorAll(t);return s?Array.from(s):[]}return Array.from(t)}function yn(t){return(e,n)=>{const i=gn(e),s=[];for(const e of i){const i=t(e,n);s.push(i)}return()=>{for(const t of s)t()}}}const vn=(t,e)=>e&&"number"==typeof t?e.transform(t):t;class Tn{constructor(){this.latest={},this.values=new Map}set(t,e,n,i,s=!0){const o=this.values.get(t);o&&o.onRemove();const l=()=>{const i=e.get();this.latest[t]=s?vn(i,ln[t]):i,n&&r.render(n)};l();const u=e.on("change",l);i&&e.addDependent(i);const c=()=>{u(),n&&a(n),this.values.delete(t),i&&e.removeDependent(i)};return this.values.set(t,{value:e,onRemove:c}),c}get(t){return this.values.get(t)?.value}destroy(){for(const t of this.values.values())t.onRemove()}}function bn(t){const e=new WeakMap,n=[];return(i,s)=>{const r=e.get(i)??new Tn;e.set(i,r);for(const e in s){const a=s[e],o=t(i,r,e,a);n.push(o)}return()=>{for(const t of n)t()}}}const wn=(t,e,n,i)=>{const s=function(t,e){if(!(e in t))return!1;const n=Object.getOwnPropertyDescriptor(Object.getPrototypeOf(t),e)||Object.getOwnPropertyDescriptor(t,e);return n&&"function"==typeof n.set}(t,n),r=s?n:n.startsWith("data")||n.startsWith("aria")?n.replace(/([A-Z])/g,t=>`-${t.toLowerCase()}`):n;const a=s?()=>{t[r]=e.latest[n]}:()=>{const i=e.latest[n];null==i?t.removeAttribute(r):t.setAttribute(r,String(i))};return e.set(n,i,a)},Mn=yn(bn(wn)),xn=bn((t,e,n,i)=>e.set(n,i,()=>{t[n]=e.latest[n]},void 0,!1));function Sn(t){return e.isObject(t)&&"offsetHeight"in t}const An={current:void 0};class kn{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=t=>{const e=h.now();if(this.updatedAt!==e&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const t of this.dependents)t.dirty()},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=h.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new e.SubscriptionManager);const i=this.events[t].add(n);return"change"===t?()=>{i(),r.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t){this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t)}setWithVelocity(t,e,n){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return An.current&&An.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){const t=h.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e.velocityPerSecond(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function En(t,e){return new kn(t,e)}const Vn={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"};const Pn=new Set(["originX","originY","originZ"]),Dn=(t,e,n,i)=>{let s,r;return le.has(n)?(e.get("transform")||(Sn(t)||e.get("transformBox")||Dn(t,e,"transformBox",new kn("fill-box")),e.set("transform",new kn("none"),()=>{t.style.transform=function(t){let e="",n=!0;for(let i=0;i<oe.length;i++){const s=oe[i],r=t.latest[s];if(void 0===r)continue;let a=!0;a="number"==typeof r?r===(s.startsWith("scale")?1:0):0===parseFloat(r),a||(n=!1,e+=`${Vn[s]||s}(${t.latest[s]}) `)}return n?"none":e.trim()}(e)})),r=e.get("transform")):Pn.has(n)?(e.get("transformOrigin")||e.set("transformOrigin",new kn(""),()=>{const n=e.latest.originX??"50%",i=e.latest.originY??"50%",s=e.latest.originZ??0;t.style.transformOrigin=`${n} ${i} ${s}`}),r=e.get("transformOrigin")):s=we(n)?()=>{t.style.setProperty(n,e.latest[n])}:()=>{t.style[n]=e.latest[n]},e.set(n,i,s,r)},Rn=yn(bn(Dn)),Fn=F.transform;const On=yn(bn((t,e,n,i)=>{if(n.startsWith("path"))return function(t,e,n,i){return r.render(()=>t.setAttribute("pathLength","1")),"pathOffset"===n?e.set(n,i,()=>t.setAttribute("stroke-dashoffset",Fn(-e.latest[n]))):(e.get("stroke-dasharray")||e.set("stroke-dasharray",new kn("1 1"),()=>{const{pathLength:n=1,pathSpacing:i}=e.latest;t.setAttribute("stroke-dasharray",`${Fn(n)} ${Fn(i??1-Number(n))}`)}),e.set(n,i,void 0,e.get("stroke-dasharray")))}(t,e,n,i);if(n.startsWith("attr"))return wn(t,e,function(t){return t.replace(/^attr([A-Z])/,(t,e)=>e.toLowerCase())}(n),i);return(n in t.style?Dn:wn)(t,e,n,i)}));const{schedule:Cn,cancel:Kn}=s(queueMicrotask,!1),Ln={x:!1,y:!1};function Wn(){return Ln.x||Ln.y}function Bn(t,e){const n=gn(t),i=new AbortController;return[n,{passive:!0,...e,signal:i.signal},()=>i.abort()]}function $n(t){return!("touch"===t.pointerType||Wn())}const Nn=(t,e)=>!!e&&(t===e||Nn(t,e.parentElement)),jn=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,In=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const Yn=new WeakSet;function zn(t){return e=>{"Enter"===e.key&&t(e)}}function Xn(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function Gn(t){return jn(t)&&!Wn()}function Un(t){return e.isObject(t)&&"ownerSVGElement"in t}const qn=new WeakMap;let Zn;const _n=(t,e,n)=>(i,s)=>s&&s[0]?s[0][t+"Size"]:Un(i)&&"getBBox"in i?i.getBBox()[e]:i[n],Hn=_n("inline","width","offsetWidth"),Jn=_n("block","height","offsetHeight");function Qn({target:t,borderBoxSize:e}){qn.get(t)?.forEach(n=>{n(t,{get width(){return Hn(t,e)},get height(){return Jn(t,e)}})})}function ti(t){t.forEach(Qn)}function ei(t,e){Zn||"undefined"!=typeof ResizeObserver&&(Zn=new ResizeObserver(ti));const n=gn(t);return n.forEach(t=>{let n=qn.get(t);n||(n=new Set,qn.set(t,n)),n.add(e),Zn?.observe(t)}),()=>{n.forEach(t=>{const n=qn.get(t);n?.delete(e),n?.size||Zn?.unobserve(t)})}}const ni=new Set;let ii;function si(t){return ni.add(t),ii||(ii=()=>{const t={get width(){return window.innerWidth},get height(){return window.innerHeight}};ni.forEach(e=>e(t))},window.addEventListener("resize",ii)),()=>{ni.delete(t),ni.size||"function"!=typeof ii||(window.removeEventListener("resize",ii),ii=void 0)}}function ri(){const{value:t}=i;null!==t?(t.frameloop.rate.push(o.delta),t.animations.mainThread.push(d.mainThread),t.animations.waapi.push(d.waapi),t.animations.layout.push(d.layout)):a(ri)}function ai(t){return t.reduce((t,e)=>t+e,0)/t.length}function oi(t,e=ai){return 0===t.length?{min:0,max:0,avg:0}:{min:Math.min(...t),max:Math.max(...t),avg:e(t)}}const li=t=>Math.round(1e3/t);function ui(){i.value=null,i.addProjectionMetrics=null}function ci(){const{value:t}=i;if(!t)throw new Error("Stats are not being measured");ui(),a(ri);const e={frameloop:{setup:oi(t.frameloop.setup),rate:oi(t.frameloop.rate),read:oi(t.frameloop.read),resolveKeyframes:oi(t.frameloop.resolveKeyframes),preUpdate:oi(t.frameloop.preUpdate),update:oi(t.frameloop.update),preRender:oi(t.frameloop.preRender),render:oi(t.frameloop.render),postRender:oi(t.frameloop.postRender)},animations:{mainThread:oi(t.animations.mainThread),waapi:oi(t.animations.waapi),layout:oi(t.animations.layout)},layoutProjection:{nodes:oi(t.layoutProjection.nodes),calculatedTargetDeltas:oi(t.layoutProjection.calculatedTargetDeltas),calculatedProjections:oi(t.layoutProjection.calculatedProjections)}},{rate:n}=e.frameloop;return n.min=li(n.min),n.max=li(n.max),n.avg=li(n.avg),[n.min,n.max]=[n.max,n.min],e}function hi(t,e){if("first"===t)return 0;{const n=e-1;return"last"===t?n:n/2}}function di(...t){const e=!Array.isArray(t[0]),n=e?0:-1,i=t[0+n],s=Wt(t[1+n],t[2+n],t[3+n]);return e?s(i):s}function mi(t){const e=[];An.current=e;const n=t();An.current=void 0;const i=En(n);return function(t,e,n){const i=()=>e.set(n()),s=()=>r.preRender(i,!1,!0),o=t.map(t=>t.on("change",s));e.on("destroy",()=>{o.forEach(t=>t()),a(i)})}(e,i,t),i}const pi=t=>Boolean(t&&t.getVelocity);function fi(t,e,n){const i=t.get();let s,a=null,o=i;const l="string"==typeof i?i.replace(/[\d.-]/g,""):void 0,u=()=>{a&&(a.stop(),a=null)},c=()=>{u(),a=new Zt({keyframes:[yi(t.get()),yi(o)],velocity:t.getVelocity(),type:"spring",restDelta:.001,restSpeed:.01,...n,onUpdate:s})};if(t.attach((e,n)=>(o=e,s=t=>n(gi(t,l)),r.postRender(c),t.get()),u),pi(e)){const n=e.on("change",e=>t.set(gi(e,l))),i=t.on("destroy",n);return()=>{n(),i()}}return u}function gi(t,e){return e?t+e:t}function yi(t){return"number"==typeof t?t:parseFloat(t)}const vi=[...Je,W,G];function Ti(t){return"layout"===t?"group":"enter"===t||"new"===t?"new":"exit"===t||"old"===t?"old":"group"}let bi={},wi=null;const Mi=(t,e)=>{bi[t]=e},xi=()=>{wi||(wi=document.createElement("style"),wi.id="motion-view");let t="";for(const e in bi){const n=bi[e];t+=`${e} {\n`;for(const[e,i]of Object.entries(n))t+=`  ${e}: ${i};\n`;t+="}\n"}wi.textContent=t,document.head.appendChild(wi),bi={}},Si=()=>{wi&&wi.parentElement&&wi.parentElement.removeChild(wi)};function Ai(t){const e=t.match(/::view-transition-(old|new|group|image-pair)\((.*?)\)/);return e?{layer:e[2],type:e[1]}:null}function ki(t){const{effect:e}=t;return!!e&&(e.target===document.documentElement&&e.pseudoElement?.startsWith("::view-transition"))}function Ei(){return document.getAnimations().filter(ki)}const Vi=["layout","enter","exit","new","old"];function Pi(t){const{update:n,targets:i,options:s}=t;if(!document.startViewTransition)return new Promise(async t=>{await n(),t(new Ie([]))});(function(t,e){return e.has(t)&&Object.keys(e.get(t)).length>0})("root",i)||Mi(":root",{"view-transition-name":"none"}),Mi("::view-transition-group(*), ::view-transition-old(*), ::view-transition-new(*)",{"animation-timing-function":"linear !important"}),xi();const r=document.startViewTransition(async()=>{await n()});return r.finished.finally(()=>{Si()}),new Promise(t=>{r.ready.then(()=>{const n=Ei(),r=[];i.forEach((t,n)=>{for(const i of Vi){if(!t[i])continue;const{keyframes:a,options:o}=t[i];for(let[t,l]of Object.entries(a)){if(!l)continue;const a={...Ze(s,t),...Ze(o,t)},u=Ti(i);if("opacity"===t&&!Array.isArray(l)){l=["new"===u?0:1,l]}"function"==typeof a.delay&&(a.delay=a.delay(0,1)),a.duration&&(a.duration=e.secondsToMilliseconds(a.duration)),a.delay&&(a.delay=e.secondsToMilliseconds(a.delay));const c=new Oe({...a,element:document.documentElement,name:t,pseudoElement:`::view-transition-${u}(${n})`,keyframes:l});r.push(c)}}});for(const t of n){if("finished"===t.playState)continue;const{effect:n}=t;if(!(n&&n instanceof KeyframeEffect))continue;const{pseudoElement:a}=n;if(!a)continue;const o=Ai(a);if(!o)continue;const l=i.get(o.layer);if(l)Di(l,"enter")&&Di(l,"exit")&&n.getKeyframes().some(t=>t.mixBlendMode)?r.push(new ze(t)):t.cancel();else{const i="group"===o.type?"layout":"";let a={...Ze(s,i)};a.duration&&(a.duration=e.secondsToMilliseconds(a.duration)),a=Fe(a);const l=Pe(a.ease,a.duration);n.updateTiming({delay:e.secondsToMilliseconds(a.delay??0),duration:a.duration,easing:l}),r.push(new ze(t))}}t(new Ie(r))})})}function Di(t,e){return t?.[e]?.keyframes.opacity}let Ri=[],Fi=null;function Oi(){Fi=null;const[t]=Ri;var n;t&&(n=t,e.removeItem(Ri,n),Fi=n,Pi(n).then(t=>{n.notifyReady(t),t.finished.finally(Oi)}))}function Ci(){for(let t=Ri.length-1;t>=0;t--){const e=Ri[t],{interrupt:n}=e.options;if("immediate"===n){const n=Ri.slice(0,t+1).map(t=>t.update),i=Ri.slice(t+1);e.update=()=>{n.forEach(t=>t())},Ri=[e,...i];break}}Fi&&"immediate"!==Ri[0]?.options.interrupt||Oi()}class Ki{constructor(t,n={}){var i;this.currentSubject="root",this.targets=new Map,this.notifyReady=e.noop,this.readyPromise=new Promise(t=>{this.notifyReady=t}),this.update=t,this.options={interrupt:"wait",...n},i=this,Ri.push(i),Cn.render(Ci)}get(t){return this.currentSubject=t,this}layout(t,e){return this.updateTarget("layout",t,e),this}new(t,e){return this.updateTarget("new",t,e),this}old(t,e){return this.updateTarget("old",t,e),this}enter(t,e){return this.updateTarget("enter",t,e),this}exit(t,e){return this.updateTarget("exit",t,e),this}crossfade(t){return this.updateTarget("enter",{opacity:1},t),this.updateTarget("exit",{opacity:0},t),this}updateTarget(t,e,n={}){const{currentSubject:i,targets:s}=this;s.has(i)||s.set(i,{});s.get(i)[t]={keyframes:e,options:n}}then(t,e){return this.readyPromise.then(t,e)}}const Li=r,Wi=n.reduce((t,e)=>(t[e]=t=>a(t),t),{});t.AsyncMotionValueAnimation=class extends Ut{constructor({autoplay:t=!0,delay:e=0,type:n="keyframes",repeat:i=0,repeatDelay:s=0,repeatType:r="loop",keyframes:a,name:o,motionValue:l,element:u,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=h.now();const d={autoplay:t,delay:e,type:n,repeat:i,repeatDelay:s,repeatType:r,name:o,motionValue:l,element:u,...c},m=u?.KeyframeResolver||be;this.keyframeResolver=new m(a,(t,e,n)=>this.onKeyframesResolved(t,e,d,!n),o,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,n,i,s){this.keyframeResolver=void 0;const{name:r,type:a,velocity:o,delay:l,isHandoff:u,onUpdate:c}=i;this.resolvedAt=h.now(),function(t,n,i,s){const r=t[0];if(null===r)return!1;if("display"===n||"visibility"===n)return!0;const a=t[t.length-1],o=We(r,n),l=We(a,n);return e.warning(o===l,`You are trying to animate ${n} from "${r}" to "${a}". "${o?a:r}" is not an animatable value.`,"value-not-animatable"),!(!o||!l)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}(t)||("spring"===i||Re(i))&&s)}(t,r,a,o)||(!e.MotionGlobalConfig.instantAnimations&&l||c?.(zt(t,i,n)),t[0]=t[t.length-1],Be(i),i.repeat=0);const d={startTime:s?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:n,...i,keyframes:t},m=!u&&je(d)?new Le({...d,element:d.motionValue.owner.current}):new Zt(d);m.finished.then(()=>this.notifyFinished()).catch(e.noop),this.pendingTimeline&&(this.stopTimeline=m.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=m}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),Te()),this._animation}get duration(){return this.animation.duration}get iterationDuration(){return this.animation.iterationDuration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}},t.DOMKeyframesResolver=class extends be{constructor(t,e,n,i,s){super(t,e,n,i,s,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:n}=this;if(!e||!e.current)return;super.readKeyframes();for(let n=0;n<t.length;n++){let i=t[n];if("string"==typeof i&&(i=i.trim(),g(i))){const s=qe(i,e.current);void 0!==s&&(t[n]=s),n===t.length-1&&(this.finalKeyframe=i)}}if(this.resolveNoneKeyframes(),!_e.has(n)||2!==t.length)return;const[i,s]=t,r=Qe(i),a=Qe(s);if(r!==a)if(ue(r)&&ue(a))for(let e=0;e<t.length;e++){const n=t[e];"string"==typeof n&&(t[e]=parseFloat(n))}else de[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:e}=this,n=[];for(let e=0;e<t.length;e++)(null===t[e]||tn(t[e]))&&n.push(e);n.length&&function(t,e,n){let i,s=0;for(;s<t.length&&!i;){const e=t[s];"string"==typeof e&&!dn.has(e)&&I(e).values.length&&(i=t[s]),s++}if(i&&n)for(const s of e)t[s]=hn(n,i)}(t,n,e)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:n}=this;if(!t||!t.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=de[n](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const i=e[e.length-1];void 0!==i&&t.getValue(n,i).jump(i,!1)}measureEndState(){const{element:t,name:e,unresolvedKeyframes:n}=this;if(!t||!t.current)return;const i=t.getValue(e);i&&i.jump(this.measuredOrigin,!1);const s=n.length-1,r=n[s];n[s]=de[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,n])=>{t.getValue(e).set(n)}),this.resolveNoneKeyframes()}},t.GroupAnimation=Ie,t.GroupAnimationWithThen=class extends Ie{then(t,e){return this.finished.finally(t).then(()=>{})}},t.JSAnimation=Zt,t.KeyframeResolver=be,t.MotionValue=kn,t.NativeAnimation=Oe,t.NativeAnimationExtended=Le,t.NativeAnimationWrapper=ze,t.ViewTransitionBuilder=Ki,t.acceleratedValues=fn,t.activeAnimations=d,t.addAttrValue=wn,t.addStyleValue=Dn,t.alpha=T,t.analyseComplexValue=I,t.animateValue=function(t){return new Zt(t)},t.animateView=function(t,e={}){return new Ki(t,e)},t.animationMapKey=(t,e="")=>`${t}:${e}`,t.applyGeneratorOptions=Fe,t.applyPxDefaults=function(t,e){for(let n=0;n<t.length;n++)"number"==typeof t[n]&&mn.has(e)&&(t[n]=t[n]+"px")},t.attachSpring=fi,t.attrEffect=Mn,t.calcGeneratorDuration=dt,t.cancelFrame=a,t.cancelMicrotask=Kn,t.cancelSync=Wi,t.collectMotionValues=An,t.color=W,t.complex=G,t.convertOffsetToTimes=Nt,t.createGeneratorEasing=mt,t.createRenderBatcher=s,t.cubicBezierAsString=Ee,t.defaultEasing=jt,t.defaultOffset=$t,t.defaultTransformValue=se,t.defaultValueTypes=un,t.degrees=D,t.dimensionValueTypes=Je,t.fillOffset=Bt,t.fillWildcards=_t,t.findDimensionValueType=Qe,t.findValueType=t=>vi.find(He(t)),t.flushKeyframeResolvers=Te,t.frame=r,t.frameData=o,t.frameSteps=l,t.generateLinearEasing=ct,t.getAnimatableNone=hn,t.getAnimationMap=function(t){const e=Xe.get(t)||new Map;return Xe.set(t,e),e},t.getComputedStyle=function(t,e){const n=window.getComputedStyle(t);return we(e)?n.getPropertyValue(e):n[e]},t.getDefaultValueType=cn,t.getMixer=st,t.getOriginIndex=hi,t.getValueAsType=vn,t.getValueTransition=Ze,t.getVariableValue=qe,t.getViewAnimationLayerInfo=Ai,t.getViewAnimations=Ei,t.hex=V,t.hover=function(t,e,n={}){const[i,s,r]=Bn(t,n),a=t=>{if(!$n(t))return;const{target:n}=t,i=e(n,t);if("function"!=typeof i||!n)return;const r=t=>{$n(t)&&(i(t),n.removeEventListener("pointerleave",r))};n.addEventListener("pointerleave",r,s)};return i.forEach(t=>{t.addEventListener("pointerenter",a,s)}),r},t.hsla=L,t.hslaToRgba=q,t.inertia=Lt,t.interpolate=Wt,t.invisibleValues=et,t.isCSSVariableName=p,t.isCSSVariableToken=g,t.isDragActive=Wn,t.isDragging=Ln,t.isGenerator=Re,t.isHTMLElement=Sn,t.isMotionValue=pi,t.isNodeOrChild=Nn,t.isPrimaryPointer=jn,t.isSVGElement=Un,t.isSVGSVGElement=function(t){return Un(t)&&"svg"===t.tagName},t.isWaapiSupportedEasing=function t(n){return Boolean("function"==typeof n&&ke()||!n||"string"==typeof n&&(n in Ve||ke())||e.isBezierDefinition(n)||Array.isArray(n)&&n.every(t))},t.keyframes=It,t.makeAnimationInstant=Be,t.mapEasingToNativeEasing=Pe,t.mapValue=function(t,e,n,i){const s=di(e,n,i);return mi(()=>s(t.get()))},t.maxGeneratorDuration=ht,t.microtask=Cn,t.mix=lt,t.mixArray=rt,t.mixColor=tt,t.mixComplex=ot,t.mixImmediate=Z,t.mixLinearColor=H,t.mixNumber=_,t.mixObject=at,t.mixVisibility=nt,t.motionValue=En,t.number=v,t.numberValueTypes=ln,t.observeTimeline=function(t,e){let n;const i=()=>{const{currentTime:i}=e,s=(null===i?0:i.value)/100;n!==s&&t(s),n=s};return r.preUpdate(i,!0),()=>a(i)},t.parseCSSVariable=Ue,t.parseValueFromTransform=re,t.percent=R,t.positionalKeys=_e,t.press=function(t,e,n={}){const[i,s,r]=Bn(t,n),a=t=>{const i=t.currentTarget;if(!Gn(t))return;Yn.add(i);const r=e(i,t),a=(t,e)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),Yn.has(i)&&Yn.delete(i),Gn(t)&&"function"==typeof r&&r(t,{success:e})},o=t=>{a(t,i===window||i===document||n.useGlobalTarget||Nn(i,t.target))},l=t=>{a(t,!1)};window.addEventListener("pointerup",o,s),window.addEventListener("pointercancel",l,s)};return i.forEach(t=>{var e;(n.useGlobalTarget?window:t).addEventListener("pointerdown",a,s),Sn(t)&&(t.addEventListener("focus",t=>((t,e)=>{const n=t.currentTarget;if(!n)return;const i=zn(()=>{if(Yn.has(n))return;Xn(n,"down");const t=zn(()=>{Xn(n,"up")});n.addEventListener("keyup",t,e),n.addEventListener("blur",()=>Xn(n,"cancel"),e)});n.addEventListener("keydown",i,e),n.addEventListener("blur",()=>n.removeEventListener("keydown",i),e)})(t,s)),e=t,In.has(e.tagName)||-1!==e.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),r},t.progressPercentage=K,t.propEffect=xn,t.px=F,t.readTransformValue=(t,e)=>{const{transform:n="none"}=getComputedStyle(t);return re(n,e)},t.recordStats=function(){if(i.value)throw ui(),new Error("Stats are already being measured");const t=i;return t.value={frameloop:{setup:[],rate:[],read:[],resolveKeyframes:[],preUpdate:[],update:[],preRender:[],render:[],postRender:[]},animations:{mainThread:[],waapi:[],layout:[]},layoutProjection:{nodes:[],calculatedTargetDeltas:[],calculatedProjections:[]}},t.addProjectionMetrics=e=>{const{layoutProjection:n}=t.value;n.nodes.push(e.nodes),n.calculatedTargetDeltas.push(e.calculatedTargetDeltas),n.calculatedProjections.push(e.calculatedProjections)},r.postRender(ri,!0),ci},t.resize=function(t,e){return"function"==typeof t?si(t):ei(t,e)},t.resolveElements=gn,t.rgbUnit=k,t.rgba=E,t.scale=b,t.setDragLock=function(t){return"x"===t||"y"===t?Ln[t]?null:(Ln[t]=!0,()=>{Ln[t]=!1}):Ln.x||Ln.y?null:(Ln.x=Ln.y=!0,()=>{Ln.x=Ln.y=!1})},t.setStyle=Me,t.spring=Kt,t.springValue=function(t,e){const n=En(pi(t)?t.get():t);return fi(n,t,e),n},t.stagger=function(t=.1,{startDelay:n=0,from:i=0,ease:s}={}){return(r,a)=>{const o="number"==typeof i?i:hi(i,a),l=Math.abs(o-r);let u=t*l;if(s){const n=a*t;u=e.easingDefinitionToFunction(s)(u/n)*n}return n+u}},t.startWaapiAnimation=De,t.statsBuffer=i,t.styleEffect=Rn,t.supportedWaapiEasing=Ve,t.supportsBrowserAnimation=je,t.supportsFlags=Se,t.supportsLinearEasing=ke,t.supportsPartialKeyframes=pn,t.supportsScrollTimeline=xe,t.svgEffect=On,t.sync=Li,t.testValueType=He,t.time=h,t.transform=di,t.transformPropOrder=oe,t.transformProps=le,t.transformValue=mi,t.transformValueTypes=on,t.vh=O,t.vw=C});
