{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/about/about-new.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/about/about-new.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/about/about-new.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAsS,GACnU,oEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/about/about-new.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/about/about-new.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/about/about-new.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAkR,GAC/S,gDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/about/philosophy.tsx"], "sourcesContent": ["import React from 'react'\r\n\r\nconst Philosophy = () => {\r\n  return (\r\n    <section className=\"relative overflow-hidden py-24\">\r\n      <div className=\"pointer-events-none absolute inset-0 bg-gradient-to-br from-sky-50 via-white to-sky-50\" />\r\n      <div className=\"relative mx-auto max-w-6xl px-4\">\r\n        <div className=\"grid grid-cols-1 gap-10 md:grid-cols-3\">\r\n          <div className=\"md:col-span-1\">\r\n            <div className=\"inline-flex items-center gap-2 rounded-full bg-sky-100 px-3 py-1 text-[11px] font-semibold tracking-wider text-sky-800 ring-1 ring-inset ring-sky-200\">Our Philosophy</div>\r\n            <h2 className=\"mt-4 text-3xl font-bold tracking-tight text-gray-900 md:text-4xl\">Why partners choose S T HVAC</h2>\r\n          </div>\r\n          <div className=\"md:col-span-2\">\r\n            <div className=\"prose prose-gray max-w-none\">\r\n              <p>\r\n                Being always there for our customers. Excellent support and service is the backbone of ST HVAC. It's one of our distinguishing features and strength. Just as you can rely on STHVAC SERVICES for our valued commitments and solutions which never fail.\r\n              </p>\r\n              <p>\r\n                For thorough reliability and responsiveness, ST HVAC SERVICES is managed though a highly efficient Personnel. Continuous Visit of Engineers and Disciplinary Staffs will be done as committed. As one of India's largest chains in organized repair, we also extends ‘direct to customer’ service and support.\r\n              </p>\r\n              <p>\r\n                To offer a great working environment for employees, clients, vendors and stakeholders by sustaining long-term growth and customer satisfaction. We offer high-quality services at unbeatable prices and give you a pleasant and satisfying personalized experience that suits your business requirements. We possess a deep understanding of the needs of modern businesses. We understand what you need to stand out from the crowd. Using innovation and technology, we will make sure that you leave your competition miles behind.\r\n              </p>\r\n              <p>\r\n                We operates across India. More than 150+ support vendor and logistics to reach every corner in India. We are one of the strongest player in the market to serve quality support with guaranteed SLA because we only focus on what we are good in. We are happy to announce that now we are the service partner of more than 12+ brands and still signing.\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"mt-8 grid grid-cols-1 gap-4 sm:grid-cols-3\">\r\n              <div className=\"rounded-2xl bg-white p-5 shadow ring-1 ring-gray-100\">\r\n                <div className=\"text-2xl font-bold text-sky-700\">24x7</div>\r\n                <div className=\"mt-1 text-sm text-gray-600\">Support & responsiveness</div>\r\n              </div>\r\n              <div className=\"rounded-2xl bg-white p-5 shadow ring-1 ring-gray-100\">\r\n                <div className=\"text-2xl font-bold text-sky-700\">150+</div>\r\n                <div className=\"mt-1 text-sm text-gray-600\">Vendors & logistics across India</div>\r\n              </div>\r\n              <div className=\"rounded-2xl bg-white p-5 shadow ring-1 ring-gray-100\">\r\n                <div className=\"text-2xl font-bold text-sky-700\">12+ Brands</div>\r\n                <div className=\"mt-1 text-sm text-gray-600\">Trusted service partnerships</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  )\r\n}\r\n\r\nexport default Philosophy\r\n\r\n\r\n"], "names": [], "mappings": ";;;;;;AAEA,MAAM,aAAa;IACjB,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAwJ;;;;;;8CACvK,8OAAC;oCAAG,WAAU;8CAAmE;;;;;;;;;;;;sCAEnF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAE;;;;;;sDAGH,8OAAC;sDAAE;;;;;;sDAGH,8OAAC;sDAAE;;;;;;sDAGH,8OAAC;sDAAE;;;;;;;;;;;;8CAKL,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAkC;;;;;;8DACjD,8OAAC;oDAAI,WAAU;8DAA6B;;;;;;;;;;;;sDAE9C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAkC;;;;;;8DACjD,8OAAC;oDAAI,WAAU;8DAA6B;;;;;;;;;;;;sDAE9C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAkC;;;;;;8DACjD,8OAAC;oDAAI,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5D;uCAEe", "debugId": null}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/about/journey.tsx"], "sourcesContent": ["type TimelineItem = {\r\n  year: string;\r\n  title: string;\r\n  description?: string;\r\n};\r\n\r\nconst defaultItems: TimelineItem[] = [\r\n  {\r\n    year: \"2016\",\r\n    title: \"Establishment\",\r\n    description: \"Specialized HVAC services.\",\r\n  },\r\n  {\r\n    year: \"2017\",\r\n    title: \"Incorporation\",\r\n    description: \"Registered company in India.\",\r\n  },\r\n  {\r\n    year: \"2018\",\r\n    title: \"Team Grows\",\r\n    description: \"Expanded with market demand.\",\r\n  },\r\n  {\r\n    year: \"2019\",\r\n    title: \"Diversification\",\r\n    description: \"Core HVAC equipment & design.\",\r\n  },\r\n  { year: \"2020\", title: \"Milestone\", description: \"Trusted partner for O&M.\" },\r\n];\r\n\r\nexport function Journey({\r\n  items = defaultItems,\r\n  heading = \"Our Journey\",\r\n  subheading = \"Highlights from our growth and milestones.\",\r\n}: {\r\n  items?: TimelineItem[];\r\n  heading?: string;\r\n  subheading?: string;\r\n}) {\r\n  return (\r\n    <section className=\"w-full bg-background text-foreground\">\r\n      <div className=\"mx-auto max-w-5xl px-4 py-12 md:py-16\">\r\n        <header className=\"mb-8 text-center\">\r\n          <h2 className=\"text-balance text-2xl font-light md:text-3xl\">\r\n            {heading}\r\n          </h2>\r\n          <p className=\"text-pretty mt-2 text-sm text-muted-foreground md:text-base\">\r\n            {subheading}\r\n          </p>\r\n        </header>\r\n\r\n        {/* Timeline */}\r\n        <div className=\"relative\">\r\n          {/* Horizontal line */}\r\n          <hr\r\n            className=\"border-border absolute inset-x-0 top-6 border-t\"\r\n            aria-hidden=\"true\"\r\n          />\r\n\r\n          {/* Milestones */}\r\n          <ol\r\n            className=\"relative z-10 grid grid-cols-2 items-start gap-y-10 md:grid-cols-5 md:gap-y-12\"\r\n            aria-label=\"Company journey timeline\"\r\n            role=\"list\"\r\n          >\r\n            {items.map((item, idx) => (\r\n              <li\r\n                key={idx}\r\n                role=\"listitem\"\r\n                className=\"flex flex-col items-center text-center\"\r\n              >\r\n                {/* Dot on the line */}\r\n                <span\r\n                  className=\"bg-primary ring-background relative z-10 inline-flex h-3 w-3 rounded-full ring-2\"\r\n                  aria-hidden=\"true\"\r\n                />\r\n                {/* Labels */}\r\n                <div className=\"mt-4\">\r\n                  <div className=\"text-xs font-medium tracking-wide text-muted-foreground\">\r\n                    {item.year}\r\n                  </div>\r\n                  <div className=\"mt-1 text-sm font-semibold\">{item.title}</div>\r\n                  {item.description ? (\r\n                    <p className=\"mt-1 max-w-[16rem] text-xs text-muted-foreground\">\r\n                      {item.description}\r\n                    </p>\r\n                  ) : null}\r\n                </div>\r\n              </li>\r\n            ))}\r\n          </ol>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n\r\nexport default Journey;\r\n"], "names": [], "mappings": ";;;;;;;;AAMA,MAAM,eAA+B;IACnC;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QAAE,MAAM;QAAQ,OAAO;QAAa,aAAa;IAA2B;CAC7E;AAEM,SAAS,QAAQ,EACtB,QAAQ,YAAY,EACpB,UAAU,aAAa,EACvB,aAAa,4CAA4C,EAK1D;IACC,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAO,WAAU;;sCAChB,8OAAC;4BAAG,WAAU;sCACX;;;;;;sCAEH,8OAAC;4BAAE,WAAU;sCACV;;;;;;;;;;;;8BAKL,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BACC,WAAU;4BACV,eAAY;;;;;;sCAId,8OAAC;4BACC,WAAU;4BACV,cAAW;4BACX,MAAK;sCAEJ,MAAM,GAAG,CAAC,CAAC,MAAM,oBAChB,8OAAC;oCAEC,MAAK;oCACL,WAAU;;sDAGV,8OAAC;4CACC,WAAU;4CACV,eAAY;;;;;;sDAGd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,KAAK,IAAI;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;8DAA8B,KAAK,KAAK;;;;;;gDACtD,KAAK,WAAW,iBACf,8OAAC;oDAAE,WAAU;8DACV,KAAK,WAAW;;;;;2DAEjB;;;;;;;;mCAnBD;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BrB;uCAEe", "debugId": null}}, {"offset": {"line": 411, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/about/our-principals.tsx"], "sourcesContent": ["import React from 'react';\r\n\r\nconst OurPrincipals = () => {\r\n  const coreValues = [\r\n    {\r\n      title: \"PROFESSIONALISM\",\r\n      description:\r\n        \"We always commit to do  our projects in a  workmanship like manner.\",\r\n    },\r\n    {\r\n      title: \"TEAMWORK\",\r\n      description:\r\n        \"We know we can only  succeed together or fail  together as a team\",\r\n    },\r\n    {\r\n      title: \"LEADERSHIP\",\r\n      description:\r\n        \"Everyone in our team is a  leader. We believe in  leading from the bottom up.\",\r\n    },\r\n    {\r\n      title: \"INTEGRITY\",\r\n      description:\r\n        \"We believe in doing things  right & within the confines  of the laws of the land\",\r\n    },\r\n    {\r\n      title: \"HONESTY\",\r\n      description:\r\n        \"We always strive to be  honest to our clients in  project delivery\",\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <section className=\"py-16 bg-white\">\r\n      <div className=\"container mx-auto px-4 lg:px-8 max-w-4xl\">\r\n        {/* Decorative line at top */}\r\n        <div className=\"flex justify-center mb-12\">\r\n          <div className=\"w-0.5 h-16 bg-gray-300\"></div>\r\n        </div>\r\n\r\n        {/* Header */}\r\n        <div className=\"text-center mb-16\">\r\n          <h2 className=\"text-4xl md:text-5xl font-light text-gray-400 tracking-wider mb-8\">\r\n            OUR PRINCIPALS\r\n          </h2>\r\n        </div>\r\n\r\n        {/* Core Values List */}\r\n        <div className=\"space-y-12\">\r\n          {coreValues.map((value, index) => (\r\n            <div key={index} className=\"text-center\">\r\n              <h3 className=\"text-xl md:text-2xl font-semibold text-gray-800 mb-4 tracking-wide\">\r\n                {value.title}\r\n              </h3>\r\n              <p className=\"text-gray-600 leading-relaxed max-w-2xl mx-auto text-sm md:text-base\">\r\n                {value.description}\r\n              </p>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default OurPrincipals;"], "names": [], "mappings": ";;;;;;AAEA,MAAM,gBAAgB;IACpB,MAAM,aAAa;QACjB;YACE,OAAO;YACP,aACE;QACJ;QACA;YACE,OAAO;YACP,aACE;QACJ;QACA;YACE,OAAO;YACP,aACE;QACJ;QACA;YACE,OAAO;YACP,aACE;QACJ;QACA;YACE,OAAO;YACP,aACE;QACJ;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;8BAIjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;kCAAoE;;;;;;;;;;;8BAMpF,8OAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,OAAO,sBACtB,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAG,WAAU;8CACX,MAAM,KAAK;;;;;;8CAEd,8OAAC;oCAAE,WAAU;8CACV,MAAM,WAAW;;;;;;;2BALZ;;;;;;;;;;;;;;;;;;;;;AAatB;uCAEe", "debugId": null}}, {"offset": {"line": 523, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/about/advantages-contract.tsx"], "sourcesContent": ["import React from 'react';\r\n\r\nconst AdvantagesContract = () => {\r\n  const advantages = [\r\n    {\r\n      id: 1,\r\n      title: \"Cost Savings\",\r\n      description: \"AMC services save from unexpected maintenance and repair cost. Besides this, the clients can avail the systematically & timely executed annual maintenance contract services that also renders rounds the clock maintenance & repair from qualified technicians and support staff.\"\r\n    },\r\n    {\r\n      id: 2,\r\n      title: \"Planned Maintenance Services\",\r\n      description: \"Annual maintenance contract also provides the clients with effective advantages of planning of the maintenance and repair services at regular intervals of time of the products such as air conditioners or heavy machinery.\"\r\n    },\r\n    {\r\n      id: 3,\r\n      title: \"Genuine Spare Parts\",\r\n      description: \"The annual maintenance contract enables the clients with only the genuine spare parts of the machinery or air conditioners for better performance and durability.\"\r\n    },\r\n    {\r\n      id: 4,\r\n      title: \"24/7 Technical Support\",\r\n      description: \"Round the clock maintenance & repair services from qualified technicians and support staff ensures your equipment is always running optimally.\"\r\n    },\r\n    {\r\n      id: 5,\r\n      title: \"Extended Equipment Life\",\r\n      description: \"Regular maintenance and use of genuine parts significantly extends the lifespan of your HVAC equipment and machinery.\"\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <section className=\"py-16 bg-gradient-to-br from-blue-50 to-white\">\r\n      <div className=\"container mx-auto px-4 lg:px-8\">\r\n        {/* Header */}\r\n        <div className=\"text-center mb-12\">\r\n          <h2 className=\"text-4xl md:text-5xl font-light text-gray-800 mb-4\">\r\n            Advantages of Annual \r\n            <span className=\"text-blue-600 block mt-2\">Maintainence Contract</span>\r\n          </h2>\r\n          <div className=\"w-24 h-1 bg-blue-600 mx-auto mb-6\"></div>\r\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed\">\r\n            Discover the comprehensive benefits of our annual maintenance contracts \r\n            that ensure optimal performance and longevity of your HVAC systems.\r\n          </p>\r\n        </div>\r\n\r\n        {/* Advantages Grid */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12\">\r\n          {advantages.map((advantage, index) => (\r\n            <div \r\n              key={advantage.id}\r\n              className=\"bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 p-6 border-l-4 border-blue-500 group\"\r\n            >\r\n              <div className=\"flex items-center mb-4\">\r\n                <div className=\"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4 group-hover:bg-blue-200 transition-colors\">\r\n                  <span className=\"text-blue-600 font-bold text-lg\">\r\n                    {String(index + 1).padStart(2, '0')}\r\n                  </span>\r\n                </div>\r\n                <h3 className=\"text-xl font-semibold text-gray-800\">\r\n                  {advantage.title}\r\n                </h3>\r\n              </div>\r\n              <p className=\"text-gray-600 leading-relaxed\">\r\n                {advantage.description}\r\n              </p>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default AdvantagesContract;"], "names": [], "mappings": ";;;;;;AAEA,MAAM,qBAAqB;IACzB,MAAM,aAAa;QACjB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAqD;8CAEjE,8OAAC;oCAAK,WAAU;8CAA2B;;;;;;;;;;;;sCAE7C,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;8BAOzE,8OAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,WAAW,sBAC1B,8OAAC;4BAEC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DACb,OAAO,QAAQ,GAAG,QAAQ,CAAC,GAAG;;;;;;;;;;;sDAGnC,8OAAC;4CAAG,WAAU;sDACX,UAAU,KAAK;;;;;;;;;;;;8CAGpB,8OAAC;oCAAE,WAAU;8CACV,UAAU,WAAW;;;;;;;2BAdnB,UAAU,EAAE;;;;;;;;;;;;;;;;;;;;;AAsB/B;uCAEe", "debugId": null}}, {"offset": {"line": 677, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/app/about/page.tsx"], "sourcesContent": ["import React from 'react'\r\nimport AboutSection from '@/components/about/about-new'\r\nimport Philosophy from '@/components/about/philosophy'\r\nimport Journey from '@/components/about/journey'\r\nimport OurPrincipals from '@/components/about/our-principals'\r\nimport AdvantagesContract from '@/components/about/advantages-contract'\r\n\r\n\r\n\r\nconst Aboutpage = () => {\r\n  return (\r\n    <main>\r\n      \r\n\r\n      <AboutSection />\r\n      <Philosophy />\r\n      <OurPrincipals />\r\n      <Journey />\r\n      <AdvantagesContract />\r\n    </main>\r\n  )\r\n}\r\n\r\nexport default Aboutpage"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;;;;;;;AAIA,MAAM,YAAY;IAChB,qBACE,8OAAC;;0BAGC,8OAAC,sJAAY;;;;;0BACb,8OAAC,oJAAU;;;;;0BACX,8OAAC,2JAAa;;;;;0BACd,8OAAC,iJAAO;;;;;0BACR,8OAAC,gKAAkB;;;;;;;;;;;AAGzB;uCAEe", "debugId": null}}]}