{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/about/about-new.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from 'react'\nimport { motion } from 'framer-motion'\nimport Image from 'next/image'\n\nconst AboutSection = () => {\n  const highlights = [\n    {\n      icon: \"🕒\",\n      title: \"24x7\",\n      subtitle: \"Dedicated Support\",\n      description: \"Round-the-clock service commitment\"\n    },\n    {\n      icon: \"⚙️\",\n      title: \"SITC\",\n      subtitle: \"Complete Solutions\",\n      description: \"Supply, Installation, Testing & Commissioning\"\n    },\n    {\n      icon: \"👨‍🔧\",\n      title: \"Skilled\",\n      subtitle: \"Expert Technicians\",\n      description: \"Equipped with tools and expertise\"\n    }\n  ];\n\n  const stats = [\n    { number: \"8+\", label: \"Years Experience\" },\n    { number: \"150+\", label: \"Projects Completed\" },\n    { number: \"100%\", label: \"Client Satisfaction\" },\n    { number: \"24/7\", label: \"Support Available\" }\n  ];\n\n  return (\n    <section className=\"relative overflow-hidden pt-32 lg:pt-40 pb-32\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-cyan-50/30\" />\n      <div className=\"absolute inset-0 opacity-5\">\n        <div className=\"absolute top-20 left-20 w-96 h-96 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl animate-pulse\"></div>\n        <div className=\"absolute bottom-20 right-20 w-96 h-96 bg-cyan-300 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-1000\"></div>\n      </div>\n\n      <div className=\"relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\">\n          {/* Left Content */}\n          <motion.div \n            initial={{ opacity: 0, x: -50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"space-y-8\"\n          >\n            <div className=\"space-y-6\">\n              <motion.div \n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.2 }}\n                className=\"inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-blue-100 to-cyan-100 px-6 py-3 text-sm font-semibold tracking-wider text-blue-800 shadow-sm\"\n              >\n                <span className=\"w-2 h-2 bg-blue-600 rounded-full animate-pulse\"></span>\n                About Us\n              </motion.div>\n              \n              <motion.h1 \n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.8, delay: 0.3 }}\n                className=\"text-5xl md:text-6xl lg:text-7xl font-bold leading-tight tracking-tight text-gray-900\"\n              >\n                S T HVAC\n                <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-cyan-600\">\n                  SERVICES\n                </span>\n              </motion.h1>\n              \n              <motion.p \n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.4 }}\n                className=\"text-xl text-gray-600 font-medium\"\n              >\n                Installation, maintenance and end‑to‑end SITC solutions\n              </motion.p>\n            </div>\n\n            <motion.div \n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.5 }}\n              className=\"prose prose-lg prose-gray max-w-none space-y-6\"\n            >\n              <p className=\"text-lg text-gray-700 leading-relaxed\">\n                We specialize in comprehensive HVAC & LT Electrical System solutions, \n                committed to delivering excellence with unwavering 24x7 dedication.\n              </p>\n              <p className=\"text-lg text-gray-700 leading-relaxed\">\n                Our team of super skilled, semi-skilled and skilled technicians comes \n                equipped with all necessary tools and expertise to meet your demands on time, \n                every time.\n              </p>\n              <p className=\"text-lg text-gray-700 leading-relaxed\">\n                We are committed to providing you the best service at the most competitive \n                rates without compromising on quality or reliability.\n              </p>\n            </motion.div>\n\n            {/* Stats Grid */}\n            <motion.div \n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.6 }}\n              className=\"grid grid-cols-2 md:grid-cols-4 gap-4\"\n            >\n              {stats.map((stat, index) => (\n                <div key={index} className=\"text-center p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-white/20\">\n                  <div className=\"text-2xl font-bold text-gray-900\">{stat.number}</div>\n                  <div className=\"text-sm text-gray-600 font-medium\">{stat.label}</div>\n                </div>\n              ))}\n            </motion.div>\n          </motion.div>\n\n          {/* Right Content - Image and Highlights */}\n          <motion.div \n            initial={{ opacity: 0, x: 50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.3 }}\n            className=\"relative\"\n          >\n            {/* Main Image */}\n            <div className=\"relative h-[600px] rounded-3xl overflow-hidden shadow-2xl group\">\n              <Image\n                src=\"/about1.jpg\"\n                alt=\"S T HVAC Services - Professional HVAC Installation\"\n                fill\n                className=\"object-cover transition-transform duration-700 group-hover:scale-105\"\n                sizes=\"(max-width: 768px) 100vw, 50vw\"\n                priority\n              />\n              <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent\"></div>\n            </div>\n\n            {/* Floating Highlights */}\n            <div className=\"absolute -bottom-8 -left-8 right-8 grid grid-cols-1 gap-4\">\n              {highlights.map((highlight, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, y: 30, scale: 0.8 }}\n                  animate={{ opacity: 1, y: 0, scale: 1 }}\n                  transition={{ duration: 0.6, delay: 0.8 + index * 0.1 }}\n                  className=\"bg-white/95 backdrop-blur-md rounded-2xl p-6 shadow-xl border border-white/20 group hover:shadow-2xl transition-all duration-300\"\n                >\n                  <div className=\"flex items-center gap-4\">\n                    <div className=\"text-3xl group-hover:scale-110 transition-transform duration-300\">\n                      {highlight.icon}\n                    </div>\n                    <div>\n                      <div className=\"text-2xl font-bold text-gray-900\">{highlight.title}</div>\n                      <div className=\"text-sm font-semibold text-blue-600\">{highlight.subtitle}</div>\n                      <div className=\"text-xs text-gray-600\">{highlight.description}</div>\n                    </div>\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  )\n}\n\nexport default AboutSection\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAMA,MAAM,eAAe;IACnB,MAAM,aAAa;QACjB;YACE,MAAM;YACN,OAAO;YACP,UAAU;YACV,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,UAAU;YACV,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,UAAU;YACV,aAAa;QACf;KACD;IAED,MAAM,QAAQ;QACZ;YAAE,QAAQ;YAAM,OAAO;QAAmB;QAC1C;YAAE,QAAQ;YAAQ,OAAO;QAAqB;QAC9C;YAAE,QAAQ;YAAQ,OAAO;QAAsB;QAC/C;YAAE,QAAQ;YAAQ,OAAO;QAAoB;KAC9C;IAED,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,uMAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAI;4CACxC,WAAU;;8DAEV,6LAAC;oDAAK,WAAU;;;;;;gDAAwD;;;;;;;sDAI1E,6LAAC,uMAAM,CAAC,EAAE;4CACR,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAI;4CACxC,WAAU;;gDACX;8DAEC,6LAAC;oDAAK,WAAU;8DAAiF;;;;;;;;;;;;sDAKnG,6LAAC,uMAAM,CAAC,CAAC;4CACP,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAI;4CACxC,WAAU;sDACX;;;;;;;;;;;;8CAKH,6LAAC,uMAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;sDAEV,6LAAC;4CAAE,WAAU;sDAAwC;;;;;;sDAIrD,6LAAC;4CAAE,WAAU;sDAAwC;;;;;;sDAKrD,6LAAC;4CAAE,WAAU;sDAAwC;;;;;;;;;;;;8CAOvD,6LAAC,uMAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;8CAET,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;4CAAgB,WAAU;;8DACzB,6LAAC;oDAAI,WAAU;8DAAoC,KAAK,MAAM;;;;;;8DAC9D,6LAAC;oDAAI,WAAU;8DAAqC,KAAK,KAAK;;;;;;;2CAFtD;;;;;;;;;;;;;;;;sCAShB,6LAAC,uMAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAGV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2IAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,IAAI;4CACJ,WAAU;4CACV,OAAM;4CACN,QAAQ;;;;;;sDAEV,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAIjB,6LAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC,WAAW,sBAC1B,6LAAC,uMAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;gDAAI,OAAO;4CAAI;4CACzC,SAAS;gDAAE,SAAS;gDAAG,GAAG;gDAAG,OAAO;4CAAE;4CACtC,YAAY;gDAAE,UAAU;gDAAK,OAAO,MAAM,QAAQ;4CAAI;4CACtD,WAAU;sDAEV,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,UAAU,IAAI;;;;;;kEAEjB,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;0EAAoC,UAAU,KAAK;;;;;;0EAClE,6LAAC;gEAAI,WAAU;0EAAuC,UAAU,QAAQ;;;;;;0EACxE,6LAAC;gEAAI,WAAU;0EAAyB,UAAU,WAAW;;;;;;;;;;;;;;;;;;2CAb5D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBvB;KArKM;uCAuKS", "debugId": null}}]}