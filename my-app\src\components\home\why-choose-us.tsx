"use client";

import React from 'react';
import { motion } from 'framer-motion';

export function WhyChooseUsSection() {
  const reasons = [
    {
      icon: "⚡",
      title: "Rapid Response",
      description: "We understand the importance of breakdown recovery lead time. Our unique internal system ensures we deliver on time, every time.",
      highlight: "We Won't Be Beaten"
    },
    {
      icon: "🔧",
      title: "ST HVAC Method",
      description: "Our unique maintenance method is the quickest and safest process around, drastically reducing breakdown time and costs.",
      highlight: "Competitive Rates"
    },
    {
      icon: "👥",
      title: "Expert Personnel",
      description: "All locations are staffed by trained and well-equipped personnel. Rapid response and speedy results can be counted on.",
      highlight: "Minimized Downtime"
    },
    {
      icon: "🎯",
      title: "Quality Focus",
      description: "No compromise attitude when it comes to quality. We maintain the highest standards in all our service deliveries.",
      highlight: "100% Quality Assured"
    }
  ];

  const specialties = [
    { icon: "🏭", title: "Industrial HVAC", description: "Specialized solutions for manufacturing and industrial facilities" },
    { icon: "🏢", title: "Commercial Systems", description: "Comprehensive HVAC solutions for office buildings and retail spaces" },
    { icon: "⚙️", title: "Maintenance Contracts", description: "Annual maintenance contracts with 24/7 support coverage" },
    { icon: "🔌", title: "Electrical Services", description: "LT electrical installations and maintenance services" }
  ];

  return (
    <section className="relative w-full py-20 bg-gradient-to-br from-gray-50 via-blue-50/30 to-gray-50 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-10 w-72 h-72 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-72 h-72 bg-cyan-300 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-1000"></div>
      </div>

      <div className="relative container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 px-6 py-3 rounded-full bg-gradient-to-r from-blue-100 to-cyan-100 text-blue-800 text-sm font-semibold mb-6">
            <span className="w-2 h-2 bg-blue-600 rounded-full animate-pulse"></span>
            Why Choose Us
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Your Trusted <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-cyan-600">HVAC Partner</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Experience the difference with our proven expertise, rapid response times, and unwavering commitment to quality.
          </p>
        </motion.div>

        {/* Main Reasons Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-20">
          {reasons.map((reason, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group relative bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 hover:-translate-y-2"
            >
              <div className="flex items-start gap-6">
                <div className="text-4xl group-hover:scale-110 transition-transform duration-300">
                  {reason.icon}
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-gray-900 mb-3">{reason.title}</h3>
                  <p className="text-gray-600 leading-relaxed mb-4">{reason.description}</p>
                  <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-100 to-cyan-100 text-blue-800 text-sm font-semibold rounded-full">
                    {reason.highlight}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Specialties Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h3 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Specialties</h3>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-600 to-cyan-600 mx-auto mb-6"></div>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Comprehensive HVAC and electrical solutions tailored to your specific industry needs.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {specialties.map((specialty, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group text-center p-6 bg-white/60 backdrop-blur-sm rounded-xl border border-white/20 hover:bg-white/80 transition-all duration-300 hover:shadow-lg"
            >
              <div className="text-3xl mb-4 group-hover:scale-110 transition-transform duration-300">
                {specialty.icon}
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-2">{specialty.title}</h4>
              <p className="text-gray-600 text-sm">{specialty.description}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
