'use client'

import React from 'react'
import Image from 'next/image'

const Client = () => {
  const clientImages = Array.from({ length: 29 }, (_, i) => `client${i + 1}.jpg`)
  
  // Duplicate the array to create seamless loop
  const duplicatedImages = [...clientImages, ...clientImages]

  return (
    <section className="py-16 bg-white overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Our Clients
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            We are proud to work with industry leaders and trusted partners who rely on our HVAC expertise and quality service.
          </p>
        </div>

        {/* Carousel Container */}
        <div className="relative">
          {/* Gradient overlays for fade effect */}
          <div className="absolute left-0 top-0 bottom-0 w-20 bg-gradient-to-r from-white to-transparent z-10"></div>
          <div className="absolute right-0 top-0 bottom-0 w-20 bg-gradient-to-l from-white to-transparent z-10"></div>
          
          {/* Moving Carousel */}
          <div className="flex animate-scroll">
            {duplicatedImages.map((image, index) => (
              <div
                key={index}
                className="group flex-shrink-0 mx-4 bg-white rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 p-6 flex items-center justify-center w-48 h-32 border border-gray-100 hover:border-gray-200"
              >
                <Image
                  src={`/${image}`}
                  alt={`Client ${(index % 29) + 1}`}
                  width={120}
                  height={80}
                  className="object-contain max-w-full max-h-full group-hover:scale-110 transition-transform duration-300"
                  style={{ filter: 'grayscale(100%)' }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.filter = 'grayscale(0%)'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.filter = 'grayscale(100%)'
                  }}
                />
              </div>
            ))}
          </div>
        </div>

        {/* Additional Info */}
        <div className="text-center mt-12">
          <p className="text-gray-500 text-sm">
            Trusted by 29+ companies and growing
          </p>
        </div>
      </div>
    </section>
  )
}

export default Client