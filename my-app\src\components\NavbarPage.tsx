"use client";

import React, { useEffect, useState, useRef } from "react";
import { AiOutlineMenu, AiOutlineClose } from "react-icons/ai";
import {
  FaFacebookF,
  FaMapMarkerAlt,
  FaEnvelope,
  FaPhone,
} from "react-icons/fa";
import Image from "next/image";
import Link from "next/link";

type NavItem = { href: string; label: string };
const navItems: NavItem[] = [
  { href: "/", label: "Home" },
  { href: "/about", label: "About" },
  { href: "/service", label: "Services" },
  { href: "/contact", label: "Contact" },
];

const NavbarPage: React.FC = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [showCompact, setShowCompact] = useState(false);
  const heroRef = useRef<HTMLElement>(null);

  const toggleMenu = () => setIsMobileMenuOpen(!isMobileMenuOpen);

  useEffect(() => {
    if (!heroRef.current) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setShowCompact(!entry.isIntersecting); // show compact navbar when hero is out of view
      },
      { threshold: 0 }
    );

    observer.observe(heroRef.current);

    return () => observer.disconnect();
  }, []);

  return (
    <>
      {/* ----- Hero Section ----- */}
      <section ref={heroRef} className="min-h-[20vh] md:min-h-[0vh] bg-gray-100">
        {/* Full Navbar (scrolls away) */}
        <nav className="w-full">
          {/* Main Bar */}
          <div className="flex items-center justify-between px-6 py-5 max-w-7xl mx-auto">
            <Link href="/">
              <Image src="/logo.png" alt="Logo" width={180} height={50} />
            </Link>

            <div className="hidden md:flex items-center space-x-10">
              <div className="flex items-center space-x-3 px-2">
                <div className="w-12 h-12 bg-[#272fcc] rounded-full flex items-center justify-center">
                  <FaMapMarkerAlt className="text-white" size={16} />
                </div>
                <div>
                  <div className="font-semibold text-gray-900">
                    A 32 / 2 Rabindrapally Garia
                  </div>
                  <div className="text-sm text-gray-600">Visit Our Office</div>
                </div>
              </div>
              <div className="flex items-center space-x-3 px-2">
                <div className="w-12 h-12 bg-[#272fcc] rounded-full flex items-center justify-center">
                  <FaEnvelope className="text-white" size={16} />
                </div>
                <div>
                  <div className="font-semibold text-gray-900">
                    <EMAIL>
                  </div>
                  <div className="text-sm text-gray-600">Email Address</div>
                </div>
              </div>
            </div>

            {/* Mobile menu button */}
            <div className="flex md:hidden">
              <button
                onClick={toggleMenu}
                aria-label="Open menu"
                className="p-2 rounded-md text-gray-700 hover:text-blue-500"
              >
                <AiOutlineMenu size={24} />
              </button>
            </div>
          </div>

          {/* Bottom Nav */}
          <div className="hidden md:flex border-t border-gray-200 px-6">
            <div className="flex justify-between items-center max-w-7xl mx-auto w-full py-4">
              <div className="flex space-x-8">
                {navItems.map((item) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    className="px-3 py-2 text-gray-700 hover:text-orange-500 font-medium transition-colors"
                  >
                    {item.label}
                  </Link>
                ))}
              </div>
              <Link
                href="/contact"
                className="bg-[#272fcc] hover:bg-blue-800 rounded-md text-white px-6 py-3 font-semibold transition-colors"
              >
               Sign in
              </Link>
            </div>
          </div>
        </nav>
      </section>

      {/* ----- Compact Sticky Navbar ----- */}
      {showCompact && (
        <nav className="fixed top-0 left-0 w-full z-50 bg-white border-b border-gray-300 shadow-sm transition-all duration-300">
          <div className="max-w-7xl mx-auto px-6 py-3 flex justify-between items-center">
            <Link href="/">
              <Image src="/logo.png" alt="Logo" width={140} height={40} />
            </Link>
            <div className="hidden md:flex items-center space-x-6">
              {navItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className="text-gray-700 hover:text-blue-500 text-sm font-medium transition-colors"
                >
                  {item.label}
                </Link>
              ))}
            </div>
            <div className="md:hidden flex items-center">
              <button
                onClick={toggleMenu}
                className="p-2 rounded-md text-gray-700 hover:text-blue-500"
              >
                <AiOutlineMenu size={24} />
              </button>
            </div>
          </div>
        </nav>
      )}

      {/* ----- Mobile Menu ----- */}
      <div
        className={`fixed top-0 left-0 h-full bg-white z-50 w-80 shadow-lg transform transition-transform duration-300 ease-in-out ${
          isMobileMenuOpen ? "translate-x-0" : "-translate-x-full"
        }`}
      >
        <div className="p-6">
          <div className="flex items-center justify-between py-2 mb-8">
            <Image src="/logo.png" alt="Logo" width={120} height={32} />
            <button
              onClick={toggleMenu}
              aria-label="Close menu"
              className="p-2 text-gray-600 hover:text-gray-900"
            >
              <AiOutlineClose size={24} />
            </button>
          </div>
          <nav className="space-y-2">
            {navItems.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className="block px-2 py-3 text-gray-700 hover:text-orange-500 text-lg font-medium"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                {item.label}
              </Link>
            ))}
          </nav>
          <div className="mt-8">
            <Link
              href="/contact"
              className="block w-full text-center bg-[#272fcc] hover:bg-blue-800 text-white py-3 font-semibold transition-colors rounded-md"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Contact Us
            </Link>
          </div>
        </div>
      </div>

      {isMobileMenuOpen && (
        <div className="fixed inset-0 bg-black/60 z-40" onClick={toggleMenu} />
      )}
    </>
  );
};

export default NavbarPage;
