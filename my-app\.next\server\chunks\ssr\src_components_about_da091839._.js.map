{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/about/about-new.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from 'react'\nimport { motion } from 'framer-motion'\nimport Image from 'next/image'\n\nconst AboutSection = () => {\n  const highlights = [\n    {\n      icon: \"🕒\",\n      title: \"24x7\",\n      subtitle: \"Dedicated Support\",\n      description: \"Round-the-clock service commitment\"\n    },\n    {\n      icon: \"⚙️\",\n      title: \"SITC\",\n      subtitle: \"Complete Solutions\",\n      description: \"Supply, Installation, Testing & Commissioning\"\n    },\n    {\n      icon: \"👨‍🔧\",\n      title: \"Skilled\",\n      subtitle: \"Expert Technicians\",\n      description: \"Equipped with tools and expertise\"\n    }\n  ];\n\n  const stats = [\n    { number: \"8+\", label: \"Years Experience\" },\n    { number: \"150+\", label: \"Projects Completed\" },\n    { number: \"100%\", label: \"Client Satisfaction\" },\n    { number: \"24/7\", label: \"Support Available\" }\n  ];\n\n  return (\n    <section className=\"relative overflow-hidden pt-32 lg:pt-40 pb-32\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-cyan-50/30\" />\n      <div className=\"absolute inset-0 opacity-5\">\n        <div className=\"absolute top-20 left-20 w-96 h-96 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl animate-pulse\"></div>\n        <div className=\"absolute bottom-20 right-20 w-96 h-96 bg-cyan-300 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-1000\"></div>\n      </div>\n\n      <div className=\"relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\">\n          {/* Left Content */}\n          <motion.div \n            initial={{ opacity: 0, x: -50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"space-y-8\"\n          >\n            <div className=\"space-y-6\">\n              <motion.div \n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.2 }}\n                className=\"inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-blue-100 to-cyan-100 px-6 py-3 text-sm font-semibold tracking-wider text-blue-800 shadow-sm\"\n              >\n                <span className=\"w-2 h-2 bg-blue-600 rounded-full animate-pulse\"></span>\n                About Us\n              </motion.div>\n              \n              <motion.h1 \n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.8, delay: 0.3 }}\n                className=\"text-5xl md:text-6xl lg:text-7xl font-bold leading-tight tracking-tight text-gray-900\"\n              >\n                S T HVAC\n                <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-cyan-600\">\n                  SERVICES\n                </span>\n              </motion.h1>\n              \n              <motion.p \n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.4 }}\n                className=\"text-xl text-gray-600 font-medium\"\n              >\n                Installation, maintenance and end‑to‑end SITC solutions\n              </motion.p>\n            </div>\n\n            <motion.div \n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.5 }}\n              className=\"prose prose-lg prose-gray max-w-none space-y-6\"\n            >\n              <p className=\"text-lg text-gray-700 leading-relaxed\">\n                We specialize in comprehensive HVAC & LT Electrical System solutions, \n                committed to delivering excellence with unwavering 24x7 dedication.\n              </p>\n              <p className=\"text-lg text-gray-700 leading-relaxed\">\n                Our team of super skilled, semi-skilled and skilled technicians comes \n                equipped with all necessary tools and expertise to meet your demands on time, \n                every time.\n              </p>\n              <p className=\"text-lg text-gray-700 leading-relaxed\">\n                We are committed to providing you the best service at the most competitive \n                rates without compromising on quality or reliability.\n              </p>\n            </motion.div>\n\n            {/* Stats Grid */}\n            <motion.div \n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.6 }}\n              className=\"grid grid-cols-2 md:grid-cols-4 gap-4\"\n            >\n              {stats.map((stat, index) => (\n                <div key={index} className=\"text-center p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-white/20\">\n                  <div className=\"text-2xl font-bold text-gray-900\">{stat.number}</div>\n                  <div className=\"text-sm text-gray-600 font-medium\">{stat.label}</div>\n                </div>\n              ))}\n            </motion.div>\n          </motion.div>\n\n          {/* Right Content - Image and Highlights */}\n          <motion.div \n            initial={{ opacity: 0, x: 50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.3 }}\n            className=\"relative\"\n          >\n            {/* Main Image */}\n            <div className=\"relative h-[600px] rounded-3xl overflow-hidden shadow-2xl group\">\n              <Image\n                src=\"/about1.jpg\"\n                alt=\"S T HVAC Services - Professional HVAC Installation\"\n                fill\n                className=\"object-cover transition-transform duration-700 group-hover:scale-105\"\n                sizes=\"(max-width: 768px) 100vw, 50vw\"\n                priority\n              />\n              <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent\"></div>\n            </div>\n\n            {/* Floating Highlights */}\n            <div className=\"absolute -bottom-8 -left-8 right-8 grid grid-cols-1 gap-4\">\n              {highlights.map((highlight, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, y: 30, scale: 0.8 }}\n                  animate={{ opacity: 1, y: 0, scale: 1 }}\n                  transition={{ duration: 0.6, delay: 0.8 + index * 0.1 }}\n                  className=\"bg-white/95 backdrop-blur-md rounded-2xl p-6 shadow-xl border border-white/20 group hover:shadow-2xl transition-all duration-300\"\n                >\n                  <div className=\"flex items-center gap-4\">\n                    <div className=\"text-3xl group-hover:scale-110 transition-transform duration-300\">\n                      {highlight.icon}\n                    </div>\n                    <div>\n                      <div className=\"text-2xl font-bold text-gray-900\">{highlight.title}</div>\n                      <div className=\"text-sm font-semibold text-blue-600\">{highlight.subtitle}</div>\n                      <div className=\"text-xs text-gray-600\">{highlight.description}</div>\n                    </div>\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  )\n}\n\nexport default AboutSection\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAMA,MAAM,eAAe;IACnB,MAAM,aAAa;QACjB;YACE,MAAM;YACN,OAAO;YACP,UAAU;YACV,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,UAAU;YACV,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,UAAU;YACV,aAAa;QACf;KACD;IAED,MAAM,QAAQ;QACZ;YAAE,QAAQ;YAAM,OAAO;QAAmB;QAC1C;YAAE,QAAQ;YAAQ,OAAO;QAAqB;QAC9C;YAAE,QAAQ;YAAQ,OAAO;QAAsB;QAC/C;YAAE,QAAQ;YAAQ,OAAO;QAAoB;KAC9C;IAED,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,oMAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAI;4CACxC,WAAU;;8DAEV,8OAAC;oDAAK,WAAU;;;;;;gDAAwD;;;;;;;sDAI1E,8OAAC,oMAAM,CAAC,EAAE;4CACR,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAI;4CACxC,WAAU;;gDACX;8DAEC,8OAAC;oDAAK,WAAU;8DAAiF;;;;;;;;;;;;sDAKnG,8OAAC,oMAAM,CAAC,CAAC;4CACP,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAI;4CACxC,WAAU;sDACX;;;;;;;;;;;;8CAKH,8OAAC,oMAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;sDAEV,8OAAC;4CAAE,WAAU;sDAAwC;;;;;;sDAIrD,8OAAC;4CAAE,WAAU;sDAAwC;;;;;;sDAKrD,8OAAC;4CAAE,WAAU;sDAAwC;;;;;;;;;;;;8CAOvD,8OAAC,oMAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;8CAET,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;4CAAgB,WAAU;;8DACzB,8OAAC;oDAAI,WAAU;8DAAoC,KAAK,MAAM;;;;;;8DAC9D,8OAAC;oDAAI,WAAU;8DAAqC,KAAK,KAAK;;;;;;;2CAFtD;;;;;;;;;;;;;;;;sCAShB,8OAAC,oMAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAGV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,wIAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,IAAI;4CACJ,WAAU;4CACV,OAAM;4CACN,QAAQ;;;;;;sDAEV,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAIjB,8OAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC,WAAW,sBAC1B,8OAAC,oMAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;gDAAI,OAAO;4CAAI;4CACzC,SAAS;gDAAE,SAAS;gDAAG,GAAG;gDAAG,OAAO;4CAAE;4CACtC,YAAY;gDAAE,UAAU;gDAAK,OAAO,MAAM,QAAQ;4CAAI;4CACtD,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,UAAU,IAAI;;;;;;kEAEjB,8OAAC;;0EACC,8OAAC;gEAAI,WAAU;0EAAoC,UAAU,KAAK;;;;;;0EAClE,8OAAC;gEAAI,WAAU;0EAAuC,UAAU,QAAQ;;;;;;0EACxE,8OAAC;gEAAI,WAAU;0EAAyB,UAAU,WAAW;;;;;;;;;;;;;;;;;;2CAb5D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBvB;uCAEe", "debugId": null}}, {"offset": {"line": 438, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/about/philosophy.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\nconst Philosophy = () => {\n  const coreValues = [\n    {\n      icon: \"🎯\",\n      title: \"Customer-Centric Approach\",\n      description: \"Being always there for our customers with excellent support and service as our backbone.\"\n    },\n    {\n      icon: \"⚡\",\n      title: \"Reliability & Responsiveness\", \n      description: \"Managed by highly efficient personnel with continuous engineer visits and disciplinary staff.\"\n    },\n    {\n      icon: \"🚀\",\n      title: \"Innovation & Technology\",\n      description: \"Using cutting-edge solutions to help you leave your competition miles behind.\"\n    },\n    {\n      icon: \"🤝\",\n      title: \"Long-term Partnerships\",\n      description: \"Sustaining growth and satisfaction for employees, clients, vendors and stakeholders.\"\n    }\n  ];\n\n  const achievements = [\n    {\n      number: \"24x7\",\n      label: \"Support & Responsiveness\",\n      icon: \"🕒\"\n    },\n    {\n      number: \"150+\",\n      label: \"Vendors & Logistics Across India\",\n      icon: \"🌐\"\n    },\n    {\n      number: \"12+\",\n      label: \"Trusted Brand Partnerships\",\n      icon: \"🤝\"\n    }\n  ];\n\n  return (\n    <section className=\"relative overflow-hidden py-20 bg-gradient-to-br from-blue-50 via-white to-cyan-50\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 opacity-30\">\n        <div className=\"absolute top-20 left-10 w-96 h-96 bg-blue-200 rounded-full mix-blend-multiply filter blur-3xl animate-pulse\"></div>\n        <div className=\"absolute bottom-20 right-10 w-96 h-96 bg-cyan-200 rounded-full mix-blend-multiply filter blur-3xl animate-pulse delay-1000\"></div>\n      </div>\n\n      <div className=\"relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        {/* Header Section */}\n        <motion.div \n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <div className=\"inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-blue-100 to-cyan-100 px-4 py-2 text-sm font-semibold tracking-wider text-blue-800 ring-1 ring-inset ring-blue-200 mb-6\">\n            ✨ Our Philosophy\n          </div>\n          <h2 className=\"text-4xl md:text-5xl font-bold tracking-tight text-gray-900 mb-6\">\n            Why Partners Choose \n            <span className=\"bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent block mt-2\">\n              ST HVAC Services\n            </span>\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            Excellence in service delivery, innovation in solutions, and unwavering commitment to customer satisfaction drives everything we do.\n          </p>\n        </motion.div>\n\n        {/* Core Values Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 mb-16\">\n          {coreValues.map((value, index) => (\n            <motion.div\n              key={index}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className=\"group relative bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 hover:-translate-y-2\"\n            >\n              <div className=\"flex items-start gap-6\">\n                <div className=\"text-4xl group-hover:scale-110 transition-transform duration-300\">\n                  {value.icon}\n                </div>\n                <div className=\"flex-1\">\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-3\">{value.title}</h3>\n                  <p className=\"text-gray-600 leading-relaxed\">{value.description}</p>\n                </div>\n              </div>\n              <div className=\"absolute inset-0 bg-gradient-to-r from-blue-500/5 to-cyan-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Mission Statement */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"bg-gradient-to-r from-blue-600 to-cyan-600 rounded-3xl p-8 md:p-12 text-white mb-16\"\n        >\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <h3 className=\"text-2xl md:text-3xl font-bold mb-6\">Our Mission</h3>\n            <p className=\"text-lg md:text-xl leading-relaxed opacity-90\">\n              To offer a great working environment for employees, clients, vendors and stakeholders by sustaining long-term growth and customer satisfaction. We provide high-quality services at unbeatable prices with personalized experiences that suit your business requirements.\n            </p>\n          </div>\n        </motion.div>\n\n        {/* Achievements Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-12\"\n        >\n          <h3 className=\"text-3xl font-bold text-gray-900 mb-4\">Our Achievements</h3>\n          <p className=\"text-gray-600 max-w-2xl mx-auto mb-12\">\n            Operating across India with a strong network of support vendors and logistics to reach every corner of the country.\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n          {achievements.map((achievement, index) => (\n            <motion.div\n              key={index}\n              initial={{ opacity: 0, scale: 0.8 }}\n              whileInView={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className=\"group text-center p-8 bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 hover:-translate-y-2\"\n            >\n              <div className=\"text-4xl mb-4 group-hover:scale-110 transition-transform duration-300\">\n                {achievement.icon}\n              </div>\n              <div className=\"text-3xl md:text-4xl font-bold bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent mb-2\">\n                {achievement.number}\n              </div>\n              <p className=\"text-gray-600 font-medium\">{achievement.label}</p>\n            </motion.div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Philosophy;\n"], "names": [], "mappings": ";;;;;AAGA;AAHA;;;AAKA,MAAM,aAAa;IACjB,MAAM,aAAa;QACjB;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,eAAe;QACnB;YACE,QAAQ;YACR,OAAO;YACP,MAAM;QACR;QACA;YACE,QAAQ;YACR,OAAO;YACP,MAAM;QACR;QACA;YACE,QAAQ;YACR,OAAO;YACP,MAAM;QACR;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,oMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;0CAA2L;;;;;;0CAG1M,8OAAC;gCAAG,WAAU;;oCAAmE;kDAE/E,8OAAC;wCAAK,WAAU;kDAAsF;;;;;;;;;;;;0CAIxG,8OAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;kCAMzE,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,OAAO,sBACtB,8OAAC,oMAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;gCAChD,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,MAAM,IAAI;;;;;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAwC,MAAM,KAAK;;;;;;kEACjE,8OAAC;wDAAE,WAAU;kEAAiC,MAAM,WAAW;;;;;;;;;;;;;;;;;;kDAGnE,8OAAC;wCAAI,WAAU;;;;;;;+BAhBV;;;;;;;;;;kCAsBX,8OAAC,oMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC;oCAAE,WAAU;8CAAgD;;;;;;;;;;;;;;;;;kCAOjE,8OAAC,oMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAE,WAAU;0CAAwC;;;;;;;;;;;;kCAKvD,8OAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC,oMAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;gCAChD,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;kDACZ,YAAY,IAAI;;;;;;kDAEnB,8OAAC;wCAAI,WAAU;kDACZ,YAAY,MAAM;;;;;;kDAErB,8OAAC;wCAAE,WAAU;kDAA6B,YAAY,KAAK;;;;;;;+BAbtD;;;;;;;;;;;;;;;;;;;;;;AAoBnB;uCAEe", "debugId": null}}, {"offset": {"line": 811, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/about/advantages-contract.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { motion } from 'framer-motion';\r\n\r\nconst AdvantagesContract = () => {\r\n  const advantages = [\r\n    {\r\n      id: 1,\r\n      icon: \"💰\",\r\n      title: \"Cost Savings\",\r\n      description: \"AMC services save from unexpected maintenance and repair costs. Systematically & timely executed services with round-the-clock maintenance from qualified technicians.\",\r\n      highlight: \"Predictable Budgeting\"\r\n    },\r\n    {\r\n      id: 2,\r\n      icon: \"📅\",\r\n      title: \"Planned Maintenance Services\",\r\n      description: \"Effective planning of maintenance and repair services at regular intervals for air conditioners and heavy machinery to ensure optimal performance.\",\r\n      highlight: \"Scheduled Care\"\r\n    },\r\n    {\r\n      id: 3,\r\n      icon: \"🔧\",\r\n      title: \"Genuine Spare Parts\",\r\n      description: \"Access to only genuine spare parts for machinery and air conditioners, ensuring better performance, reliability, and extended equipment life.\",\r\n      highlight: \"Quality Assured\"\r\n    },\r\n    {\r\n      id: 4,\r\n      icon: \"🛠️\",\r\n      title: \"24/7 Technical Support\",\r\n      description: \"Round-the-clock maintenance & repair services from qualified technicians and support staff ensures your equipment is always running optimally.\",\r\n      highlight: \"Always Available\"\r\n    },\r\n    {\r\n      id: 5,\r\n      icon: \"⏱️\",\r\n      title: \"Extended Equipment Life\",\r\n      description: \"Regular maintenance and use of genuine parts significantly extends the lifespan of your HVAC equipment and machinery, maximizing your investment.\",\r\n      highlight: \"Long-term Value\"\r\n    },\r\n    {\r\n      id: 6,\r\n      icon: \"📊\",\r\n      title: \"Performance Monitoring\",\r\n      description: \"Continuous monitoring and performance analysis to identify potential issues before they become costly problems, ensuring peak efficiency.\",\r\n      highlight: \"Proactive Care\"\r\n    }\r\n  ];\r\n\r\n  const benefits = [\r\n    { icon: \"✅\", text: \"Guaranteed SLA compliance\" },\r\n    { icon: \"🎯\", text: \"Priority service response\" },\r\n    { icon: \"📈\", text: \"Improved equipment efficiency\" },\r\n    { icon: \"🔒\", text: \"Warranty protection\" }\r\n  ];\r\n\r\n  return (\r\n    <section className=\"relative py-20 bg-gradient-to-br from-gray-50 via-blue-50/30 to-gray-50 overflow-hidden\">\r\n      {/* Background Elements */}\r\n      <div className=\"absolute inset-0 opacity-20\">\r\n        <div className=\"absolute top-32 left-20 w-80 h-80 bg-blue-300 rounded-full mix-blend-multiply filter blur-3xl animate-pulse\"></div>\r\n        <div className=\"absolute bottom-32 right-20 w-80 h-80 bg-cyan-300 rounded-full mix-blend-multiply filter blur-3xl animate-pulse delay-1000\"></div>\r\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-indigo-200 rounded-full mix-blend-multiply filter blur-3xl animate-pulse delay-500\"></div>\r\n      </div>\r\n\r\n      <div className=\"relative container mx-auto px-4 lg:px-8 max-w-7xl\">\r\n        {/* Header */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.8 }}\r\n          viewport={{ once: true }}\r\n          className=\"text-center mb-16\"\r\n        >\r\n          <div className=\"inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-blue-100 to-cyan-100 px-4 py-2 text-sm font-semibold tracking-wider text-blue-800 ring-1 ring-inset ring-blue-200 mb-6\">\r\n            🏆 AMC Benefits\r\n          </div>\r\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-800 mb-6\">\r\n            Advantages of Annual\r\n            <span className=\"bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent block mt-2\">\r\n              Maintenance Contract\r\n            </span>\r\n          </h2>\r\n          <div className=\"w-24 h-1 bg-gradient-to-r from-blue-600 to-cyan-600 mx-auto mb-6\"></div>\r\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\r\n            Discover the comprehensive benefits of our annual maintenance contracts\r\n            that ensure optimal performance and longevity of your HVAC systems.\r\n          </p>\r\n        </motion.div>\r\n\r\n        {/* Advantages Grid */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16\">\r\n          {advantages.map((advantage, index) => (\r\n            <motion.div\r\n              key={advantage.id}\r\n              initial={{ opacity: 0, y: 30 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: index * 0.1 }}\r\n              viewport={{ once: true }}\r\n              className=\"group relative bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 hover:-translate-y-2\"\r\n            >\r\n              <div className=\"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\r\n                <span className=\"text-xs font-semibold text-blue-600 bg-blue-50 px-2 py-1 rounded-full\">\r\n                  {advantage.highlight}\r\n                </span>\r\n              </div>\r\n\r\n              <div className=\"flex items-start gap-6 mb-4\">\r\n                <div className=\"text-4xl group-hover:scale-110 transition-transform duration-300\">\r\n                  {advantage.icon}\r\n                </div>\r\n                <div className=\"flex-1\">\r\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-3\">\r\n                    {advantage.title}\r\n                  </h3>\r\n                </div>\r\n              </div>\r\n\r\n              <p className=\"text-gray-600 leading-relaxed\">\r\n                {advantage.description}\r\n              </p>\r\n\r\n              <div className=\"absolute inset-0 bg-gradient-to-r from-blue-500/5 to-cyan-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n            </motion.div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Additional Benefits Section */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.8 }}\r\n          viewport={{ once: true }}\r\n          className=\"bg-gradient-to-r from-blue-600 to-cyan-600 rounded-3xl p-8 md:p-12 text-white\"\r\n        >\r\n          <div className=\"max-w-4xl mx-auto\">\r\n            <h3 className=\"text-2xl md:text-3xl font-bold text-center mb-8\">Additional Benefits</h3>\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\r\n              {benefits.map((benefit, index) => (\r\n                <motion.div\r\n                  key={index}\r\n                  initial={{ opacity: 0, scale: 0.8 }}\r\n                  whileInView={{ opacity: 1, scale: 1 }}\r\n                  transition={{ duration: 0.5, delay: index * 0.1 }}\r\n                  viewport={{ once: true }}\r\n                  className=\"flex items-center gap-3 bg-white/10 backdrop-blur-sm rounded-xl p-4 hover:bg-white/20 transition-all duration-300\"\r\n                >\r\n                  <span className=\"text-2xl\">{benefit.icon}</span>\r\n                  <span className=\"font-medium\">{benefit.text}</span>\r\n                </motion.div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default AdvantagesContract;"], "names": [], "mappings": ";;;;;AAGA;AAHA;;;AAKA,MAAM,qBAAqB;IACzB,MAAM,aAAa;QACjB;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;YACb,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;YACb,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;YACb,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;YACb,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;YACb,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;YACb,WAAW;QACb;KACD;IAED,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,MAAM;QAA4B;QAC/C;YAAE,MAAM;YAAM,MAAM;QAA4B;QAChD;YAAE,MAAM;YAAM,MAAM;QAAgC;QACpD;YAAE,MAAM;YAAM,MAAM;QAAsB;KAC3C;IAED,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,oMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;0CAA2L;;;;;;0CAG1M,8OAAC;gCAAG,WAAU;;oCAAoD;kDAEhE,8OAAC;wCAAK,WAAU;kDAAsF;;;;;;;;;;;;0CAIxG,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;kCAOzE,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,WAAW,sBAC1B,8OAAC,oMAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;gCAChD,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDACb,UAAU,SAAS;;;;;;;;;;;kDAIxB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,UAAU,IAAI;;;;;;0DAEjB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAG,WAAU;8DACX,UAAU,KAAK;;;;;;;;;;;;;;;;;kDAKtB,8OAAC;wCAAE,WAAU;kDACV,UAAU,WAAW;;;;;;kDAGxB,8OAAC;wCAAI,WAAU;;;;;;;+BA5BV,UAAU,EAAE;;;;;;;;;;kCAkCvB,8OAAC,oMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAkD;;;;;;8CAChE,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,oMAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAI;4CAClC,aAAa;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CACpC,YAAY;gDAAE,UAAU;gDAAK,OAAO,QAAQ;4CAAI;4CAChD,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;;8DAEV,8OAAC;oDAAK,WAAU;8DAAY,QAAQ,IAAI;;;;;;8DACxC,8OAAC;oDAAK,WAAU;8DAAe,QAAQ,IAAI;;;;;;;2CARtC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBvB;uCAEe", "debugId": null}}]}