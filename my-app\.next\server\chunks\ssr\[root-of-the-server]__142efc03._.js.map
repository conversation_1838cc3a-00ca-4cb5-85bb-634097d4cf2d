{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/about/about-new.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/about/about-new.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/about/about-new.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAsS,GACnU,oEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/about/about-new.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/about/about-new.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/about/about-new.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAkR,GAC/S,gDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/about/philosophy.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/about/philosophy.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/about/philosophy.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAuS,GACpU,qEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 65, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/about/philosophy.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/about/philosophy.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/about/philosophy.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAmR,GAChT,iDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/about/journey.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Journey = registerClientReference(\n    function() { throw new Error(\"Attempted to call Journey() from the server but Journey is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/about/journey.tsx <module evaluation>\",\n    \"Journey\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/about/journey.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/about/journey.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;;;AACvE;;AACO,MAAM,UAAU,IAAA,wQAAuB,EAC1C;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,kEACA;uCAEW,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/about/journey.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Journey = registerClientReference(\n    function() { throw new Error(\"Attempted to call Journey() from the server but Journey is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/about/journey.tsx\",\n    \"Journey\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/about/journey.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/about/journey.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;;;AACvE;;AACO,MAAM,UAAU,IAAA,wQAAuB,EAC1C;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,8CACA;uCAEW,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 125, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/about/our-principals.tsx"], "sourcesContent": ["import React from 'react';\r\n\r\nconst OurPrincipals = () => {\r\n  const coreValues = [\r\n    {\r\n      title: \"PROFESSIONALISM\",\r\n      description:\r\n        \"We always commit to do  our projects in a  workmanship like manner.\",\r\n    },\r\n    {\r\n      title: \"TEAMWORK\",\r\n      description:\r\n        \"We know we can only  succeed together or fail  together as a team\",\r\n    },\r\n    {\r\n      title: \"LEADERSHIP\",\r\n      description:\r\n        \"Everyone in our team is a  leader. We believe in  leading from the bottom up.\",\r\n    },\r\n    {\r\n      title: \"INTEGRITY\",\r\n      description:\r\n        \"We believe in doing things  right & within the confines  of the laws of the land\",\r\n    },\r\n    {\r\n      title: \"HONESTY\",\r\n      description:\r\n        \"We always strive to be  honest to our clients in  project delivery\",\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <section className=\"py-16 bg-white\">\r\n      <div className=\"container mx-auto px-4 lg:px-8 max-w-4xl\">\r\n        {/* Decorative line at top */}\r\n        <div className=\"flex justify-center mb-12\">\r\n          <div className=\"w-0.5 h-16 bg-gray-300\"></div>\r\n        </div>\r\n\r\n        {/* Header */}\r\n        <div className=\"text-center mb-16\">\r\n          <h2 className=\"text-4xl md:text-5xl font-light text-gray-400 tracking-wider mb-8\">\r\n            OUR PRINCIPALS\r\n          </h2>\r\n        </div>\r\n\r\n        {/* Core Values List */}\r\n        <div className=\"space-y-12\">\r\n          {coreValues.map((value, index) => (\r\n            <div key={index} className=\"text-center\">\r\n              <h3 className=\"text-xl md:text-2xl font-semibold text-gray-800 mb-4 tracking-wide\">\r\n                {value.title}\r\n              </h3>\r\n              <p className=\"text-gray-600 leading-relaxed max-w-2xl mx-auto text-sm md:text-base\">\r\n                {value.description}\r\n              </p>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default OurPrincipals;"], "names": [], "mappings": ";;;;;;AAEA,MAAM,gBAAgB;IACpB,MAAM,aAAa;QACjB;YACE,OAAO;YACP,aACE;QACJ;QACA;YACE,OAAO;YACP,aACE;QACJ;QACA;YACE,OAAO;YACP,aACE;QACJ;QACA;YACE,OAAO;YACP,aACE;QACJ;QACA;YACE,OAAO;YACP,aACE;QACJ;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;8BAIjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;kCAAoE;;;;;;;;;;;8BAMpF,8OAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,OAAO,sBACtB,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAG,WAAU;8CACX,MAAM,KAAK;;;;;;8CAEd,8OAAC;oCAAE,WAAU;8CACV,MAAM,WAAW;;;;;;;2BALZ;;;;;;;;;;;;;;;;;;;;;AAatB;uCAEe", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/about/advantages-contract.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/about/advantages-contract.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/about/advantages-contract.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAgT,GAC7U,8EACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/about/advantages-contract.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/about/advantages-contract.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/about/advantages-contract.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA4R,GACzT,0DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 273, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 281, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/app/about/page.tsx"], "sourcesContent": ["import React from 'react'\r\nimport AboutSection from '@/components/about/about-new'\r\nimport Philosophy from '@/components/about/philosophy'\r\nimport Journey from '@/components/about/journey'\r\nimport OurPrincipals from '@/components/about/our-principals'\r\nimport AdvantagesContract from '@/components/about/advantages-contract'\r\n\r\n\r\n\r\nconst Aboutpage = () => {\r\n  return (\r\n    <main>\r\n      \r\n\r\n      <AboutSection />\r\n      <Philosophy />\r\n      <OurPrincipals />\r\n      <Journey />\r\n      <AdvantagesContract />\r\n    </main>\r\n  )\r\n}\r\n\r\nexport default Aboutpage"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;;;;;;;AAIA,MAAM,YAAY;IAChB,qBACE,8OAAC;;0BAGC,8OAAC,sJAAY;;;;;0BACb,8OAAC,oJAAU;;;;;0BACX,8OAAC,2JAAa;;;;;0BACd,8OAAC,iJAAO;;;;;0BACR,8OAAC,gKAAkB;;;;;;;;;;;AAGzB;uCAEe", "debugId": null}}]}