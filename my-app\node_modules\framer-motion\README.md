<h1 align="center"> <img width="35" height="35" alt="Motion logo" src="https://github.com/user-attachments/assets/00d6d1c3-72c4-4c2f-a664-69da13182ffc" /><br />Motion for React</h1>
<h3 align="center">
  An open source animation library<br />for React
</h3>

<p align="center">
  <a href="https://www.npmjs.com/package/motion" rel="noopener noreferrer nofollow" ><img src="https://img.shields.io/npm/v/motion?color=0368FF&label=version" alt="npm version"></a>
  <a href="https://www.npmjs.com/package/motion" rel="noopener noreferrer nofollow" ><img src="https://img.shields.io/npm/dm/framer-motion?color=8D30FF&label=npm" alt="npm downloads per month"></a>
  <a target="_blank" rel="noopener noreferrer nofollow" href="https://www.jsdelivr.com/package/npm/motion"><img alt="jsDelivr hits (npm)" src="https://img.shields.io/jsdelivr/npm/hm/framer-motion?logo=jsdeliver&color=FF4FBA"></a>
  <img alt="NPM License" src="https://img.shields.io/npm/l/motion?color=FF2B6E">
</p>

```bash
npm install motion
```

## Table of Contents

1. [Why Motion?](#why-motion)
2. [🍦 Platforms](#-platforms)
3. [🎓 Examples](#-examples)
4. [⚡️ Motion+](#-motion)
5. [👩🏻‍⚖️ License](#-license)
6. [💎 Contribute](#-contribute)
7. [✨ Sponsors](#-sponsors)

## Why Motion?

-   **Simple API:** First-class React, JavaScript, and Vue packages.
-   **Hybrid engine:** Power of JavaScript combined with native browser APIs for 120fps, GPU-accelerated animations.
-   **Production-ready:** TypeScript, extensive test suite, tree-shakable, tiny footprint.
    **Batteries included:** Gestures, springs, layout transitions, scroll-linked effects, timelines.

## 🍦 Platforms

Motion is available for [React](https://motion.dev/docs/react), [JavaScript](https://motion.dev/docs/quick-start) and [Vue](https://motion.dev/docs/vue).

<details>
<summary>React</summary>

```jsx
import { motion } from "motion/react"

function Component() {
    return <motion.div animate={{ x: 100 }} />
}
```

Get started with [Motion for React](https://motion.dev/docs/react).

</details>

<details>
<summary>JavaScript</summary>

```javascript
import { animate } from "motion"

animate("#box", { x: 100 })
```

Get started with [JavaScript](https://motion.dev/docs/quick-start).

</details>

<details>
<summary>Vue</summary>

```html
<script>
    import { motion } from "motion-v"
</script>

<template> <motion.div :animate={{ x: 100 }} /> </template>
```

Get started with [Motion for Vue](https://motion.dev/docs/vue).

</details>

## 🎓 Examples

Browse 100+ free and 180+ premium [Motion Examples](https://motion.dev/examples), with copy-paste code that'll level-up your animations whether you're a beginner or an expert.

## ⚡️ Motion+

A one-time payment, lifetime-updates membership:

-   **180+ premium examples**
-   **Premium APIs** like [Cursor](https://motion.dev/docs/cursor) and [Ticker](https://motion.dev/docs/react-ticker)
-   **Visual editing** for VS Code (alpha)
-   **Private Discord**
-   **Early access content**

[Get Motion+](https://motion.dev/plus)

## 👩🏻‍⚖️ License

-   Motion is MIT licensed.

## 💎 Contribute

-   Want to contribute to Motion? Our [contributing guide](https://github.com/motiondivision/motion/blob/master/CONTRIBUTING.md) has you covered.

## ✨ Sponsors

Motion is sustainable thanks to the kind support of its sponsors.

[Become a sponsor](https://motion.dev/sponsor)

### Partner

Motion powers Framer animations, the web builder for creative pros. Design and ship your dream site. Zero code, maximum speed.

<a href="https://framer.link/FlnUbQY">
  <img alt="Framer" src="https://github.com/user-attachments/assets/22a79be7-672e-4336-bfb7-5d55d1deb917" width="250px" height="150px">
</a>

### Platinum

<a href="https://linear.app"><img alt="Linear" src="https://github.com/user-attachments/assets/f9ce44b4-af28-4770-bb6e-9515b474bfb2" width="250px" height="150px"></a> <a href="https://figma.com"><img alt="Figma" src="https://github.com/user-attachments/assets/1077d0ab-4305-4a1f-81c8-d5be8c4c6717" width="250px" height="150px"></a> <a href="https://sanity.io"><img alt="Sanity" src="https://github.com/user-attachments/assets/80134088-f456-483f-8edd-940593c120ce" width="250px" height="150px"></a>

### Gold

<a href="https://tailwindcss.com"><img alt="Tailwind" src="https://github.com/user-attachments/assets/1d5f2571-8bc3-4367-9fec-14d291168ff0" width="200px" height="120px"></a> <a href="https://emilkowal.ski"><img alt="Emil Kowalski" src="https://github.com/user-attachments/assets/33d1cb98-238a-4eed-a0df-9c7ab097d65b" width="200px" height="120px"></a> <a href="https://liveblocks.io"><img alt="Liveblocks" src="https://github.com/user-attachments/assets/28eddbe5-1617-4e74-969d-2eb6fcd481af" width="200px" height="120px"></a> <a href="https://vercel.com"><img alt="Vercel" src="https://github.com/user-attachments/assets/abc473bd-07ae-4a7d-9833-efe9dadb3773" width="200px" height="120px"></a> <a href="https://lu.ma"><img alt="Luma" src="https://github.com/user-attachments/assets/ac282433-6adb-4ad2-9fd2-5c6ee513c14b" width="200px" height="120px"></a> <a href="https://notion.com"><img alt="Notion" src="https://github.com/user-attachments/assets/a27a6033-3cb0-4232-a6bb-625e1824517b" width="200px" height="120px"></a>

### Silver

<a href="https://www.frontend.fyi/?utm_source=motion"><img alt="Frontend.fyi" src="https://github.com/user-attachments/assets/f16e3eb9-f0bd-4ad1-8049-f079a3d65c69" width="150px" height="100px"></a> <a href="https://firecrawl.dev"><img alt="Firecrawl" src="https://github.com/user-attachments/assets/2c44e7f4-5c2a-4714-9050-1570538665ff" width="150px" height="100px"></a> <a href="https://puzzmo.com"><img alt="Puzzmo" src="https://github.com/user-attachments/assets/e32205a7-3ab1-41ec-8729-a794058fd655" width="150px" height="100px"></a> <a href="https://bolt.new"><img alt="Bolt.new" src="https://github.com/user-attachments/assets/7932d4b2-bb6c-422e-82b9-6ad78a7e3090" width="150px" height="100px"></a>

### Personal

-   [OlegWock](https://sinja.io)
-   [Lambert Weller](https://github.com/l-mbert)
-   [Jake LeBoeuf](https://jklb.wf)
-   [Han Lee](https://github.com/hahnlee)
