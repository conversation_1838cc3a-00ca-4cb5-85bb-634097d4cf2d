{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/home/<USER>'s on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/home/<USER>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAgS,GAC7T,8DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/home/<USER>'s on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/home/<USER>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA4Q,GACzS,0CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/home/<USER>'s on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/home/<USER>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 65, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/home/<USER>'s on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/home/<USER>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,4CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/home/<USER>'s on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/home/<USER>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAmS,GAChU,iEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/home/<USER>'s on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/home/<USER>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA+Q,GAC5S,6CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>"], "sourcesContent": ["export function WhyChooseUsSection() {\r\n  return (\r\n    <section className=\"w-full py-12 sm:py-16 lg:py-20\">\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl\">\r\n        <div className=\"grid grid-cols-1 gap-8 sm:gap-10 md:grid-cols-2 md:gap-12 lg:gap-16 xl:gap-20\">\r\n          {/* Copy block */}\r\n          <article\r\n            aria-labelledby=\"why-choose-us-heading\"\r\n            className=\"space-y-6 sm:space-y-8\"\r\n          >\r\n            <header className=\"space-y-3\">\r\n              <h2\r\n                id=\"why-choose-us-heading\"\r\n                className=\"text-2xl sm:text-3xl lg:text-4xl font-semibold leading-tight text-gray-900\"\r\n              >\r\n                Why choose us\r\n              </h2>\r\n              <div className=\"w-12 h-1 bg-blue-600 rounded-full\"></div>\r\n            </header>\r\n\r\n            <div className=\"space-y-6\">\r\n              <p className=\"text-base sm:text-lg leading-relaxed text-gray-700 text-pretty\">\r\n                We understand the importance of breakdown recovery lead time to our\r\n                customers. Our unique internal System, together with our dedicated\r\n                manpower ensure that we deliver on time every time – We Won't Be\r\n                Beaten.\r\n              </p>\r\n\r\n              <p className=\"text-base sm:text-lg leading-relaxed text-gray-700 text-pretty\">\r\n                Our unique 'ST HVAC Maintenance method is the quickest and safest\r\n                process around, drastically reducing breakdown time and Maintenance\r\n                costs. Our Rates are very Competitive with no compromise attitude as\r\n                far as quality is concerned.\r\n              </p>\r\n            </div>\r\n          </article>\r\n\r\n          {/* Speciality / capability block */}\r\n          <article\r\n            aria-labelledby=\"speciality-within-heading\"\r\n            className=\"space-y-6 sm:space-y-8 md:pt-8 lg:pt-12\"\r\n          >\r\n            <header className=\"space-y-3\">\r\n              <h3\r\n                id=\"speciality-within-heading\"\r\n                className=\"text-xl sm:text-2xl lg:text-3xl font-semibold leading-tight text-gray-900\"\r\n              >\r\n                Speciality within\r\n              </h3>\r\n              <div className=\"w-10 h-1 bg-green-600 rounded-full\"></div>\r\n            </header>\r\n\r\n            <div className=\"space-y-4\">\r\n              <p className=\"text-base sm:text-lg leading-relaxed text-gray-700 text-pretty\">\r\n                Also our all locations are staffed by trained and well-equipped\r\n                personnel. Rapid response and speedy results can be counted on. Easy\r\n                access is further enhanced through ServiceSolutions Providers,\r\n                minimizing down-time and ensuring quick satisfaction - speciality\r\n                within\r\n              </p>\r\n            </div>\r\n          </article>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAAO,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBACC,mBAAgB;wBAChB,WAAU;;0CAEV,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC;wCACC,IAAG;wCACH,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCAAI,WAAU;;;;;;;;;;;;0CAGjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAiE;;;;;;kDAO9E,8OAAC;wCAAE,WAAU;kDAAiE;;;;;;;;;;;;;;;;;;kCAUlF,8OAAC;wBACC,mBAAgB;wBAChB,WAAU;;0CAEV,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC;wCACC,IAAG;wCACH,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCAAI,WAAU;;;;;;;;;;;;0CAGjB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAiE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAa5F", "debugId": null}}, {"offset": {"line": 274, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/app/page.tsx"], "sourcesContent": ["import Hero from '@/components/home/<USER>'\nimport Client from '@/components/home/<USER>'\nimport Gallery from '@/components/home/<USER>'\nimport AboutTeaser from '@/components/home/<USER>'\nimport {WhyChooseUsSection} from '@/components/home/<USER>'\nimport React from 'react'\n\nconst Homepage = () => {\n  return (\n    <div>\n      <Hero />\n      <AboutTeaser />\n      <Gallery />\n      <Client />\n      <WhyChooseUsSection />\n    </div>\n  )\n}\n\nexport default Homepage"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAGA,MAAM,WAAW;IACf,qBACE,8OAAC;;0BACC,8OAAC,6IAAI;;;;;0BACL,8OAAC,qJAAW;;;;;0BACZ,8OAAC,gJAAO;;;;;0BACR,8OAAC,+IAAM;;;;;0BACP,8OAAC,uKAAkB;;;;;;;;;;;AAGzB;uCAEe", "debugId": null}}]}