{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/about/about-new.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/about/about-new.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/about/about-new.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAsS,GACnU,oEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/about/about-new.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/about/about-new.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/about/about-new.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAkR,GAC/S,gDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/about/philosophy.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/about/philosophy.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/about/philosophy.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAuS,GACpU,qEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 65, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/about/philosophy.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/about/philosophy.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/about/philosophy.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAmR,GAChT,iDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/about/journey.tsx"], "sourcesContent": ["type TimelineItem = {\r\n  year: string;\r\n  title: string;\r\n  description?: string;\r\n};\r\n\r\nconst defaultItems: TimelineItem[] = [\r\n  {\r\n    year: \"2016\",\r\n    title: \"Establishment\",\r\n    description: \"Specialized HVAC services.\",\r\n  },\r\n  {\r\n    year: \"2017\",\r\n    title: \"Incorporation\",\r\n    description: \"Registered company in India.\",\r\n  },\r\n  {\r\n    year: \"2018\",\r\n    title: \"Team Grows\",\r\n    description: \"Expanded with market demand.\",\r\n  },\r\n  {\r\n    year: \"2019\",\r\n    title: \"Diversification\",\r\n    description: \"Core HVAC equipment & design.\",\r\n  },\r\n  { year: \"2020\", title: \"Milestone\", description: \"Trusted partner for O&M.\" },\r\n];\r\n\r\nexport function Journey({\r\n  items = defaultItems,\r\n  heading = \"Our Journey\",\r\n  subheading = \"Highlights from our growth and milestones.\",\r\n}: {\r\n  items?: TimelineItem[];\r\n  heading?: string;\r\n  subheading?: string;\r\n}) {\r\n  return (\r\n    <section className=\"w-full bg-background text-foreground\">\r\n      <div className=\"mx-auto max-w-5xl px-4 py-12 md:py-16\">\r\n        <header className=\"mb-8 text-center\">\r\n          <h2 className=\"text-balance text-2xl font-light md:text-3xl\">\r\n            {heading}\r\n          </h2>\r\n          <p className=\"text-pretty mt-2 text-sm text-muted-foreground md:text-base\">\r\n            {subheading}\r\n          </p>\r\n        </header>\r\n\r\n        {/* Timeline */}\r\n        <div className=\"relative\">\r\n          {/* Horizontal line */}\r\n          <hr\r\n            className=\"border-border absolute inset-x-0 top-6 border-t\"\r\n            aria-hidden=\"true\"\r\n          />\r\n\r\n          {/* Milestones */}\r\n          <ol\r\n            className=\"relative z-10 grid grid-cols-2 items-start gap-y-10 md:grid-cols-5 md:gap-y-12\"\r\n            aria-label=\"Company journey timeline\"\r\n            role=\"list\"\r\n          >\r\n            {items.map((item, idx) => (\r\n              <li\r\n                key={idx}\r\n                role=\"listitem\"\r\n                className=\"flex flex-col items-center text-center\"\r\n              >\r\n                {/* Dot on the line */}\r\n                <span\r\n                  className=\"bg-primary ring-background relative z-10 inline-flex h-3 w-3 rounded-full ring-2\"\r\n                  aria-hidden=\"true\"\r\n                />\r\n                {/* Labels */}\r\n                <div className=\"mt-4\">\r\n                  <div className=\"text-xs font-medium tracking-wide text-muted-foreground\">\r\n                    {item.year}\r\n                  </div>\r\n                  <div className=\"mt-1 text-sm font-semibold\">{item.title}</div>\r\n                  {item.description ? (\r\n                    <p className=\"mt-1 max-w-[16rem] text-xs text-muted-foreground\">\r\n                      {item.description}\r\n                    </p>\r\n                  ) : null}\r\n                </div>\r\n              </li>\r\n            ))}\r\n          </ol>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n\r\nexport default Journey;\r\n"], "names": [], "mappings": ";;;;;;;;AAMA,MAAM,eAA+B;IACnC;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QAAE,MAAM;QAAQ,OAAO;QAAa,aAAa;IAA2B;CAC7E;AAEM,SAAS,QAAQ,EACtB,QAAQ,YAAY,EACpB,UAAU,aAAa,EACvB,aAAa,4CAA4C,EAK1D;IACC,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAO,WAAU;;sCAChB,8OAAC;4BAAG,WAAU;sCACX;;;;;;sCAEH,8OAAC;4BAAE,WAAU;sCACV;;;;;;;;;;;;8BAKL,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BACC,WAAU;4BACV,eAAY;;;;;;sCAId,8OAAC;4BACC,WAAU;4BACV,cAAW;4BACX,MAAK;sCAEJ,MAAM,GAAG,CAAC,CAAC,MAAM,oBAChB,8OAAC;oCAEC,MAAK;oCACL,WAAU;;sDAGV,8OAAC;4CACC,WAAU;4CACV,eAAY;;;;;;sDAGd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,KAAK,IAAI;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;8DAA8B,KAAK,KAAK;;;;;;gDACtD,KAAK,WAAW,iBACf,8OAAC;oDAAE,WAAU;8DACV,KAAK,WAAW;;;;;2DAEjB;;;;;;;;mCAnBD;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BrB;uCAEe", "debugId": null}}, {"offset": {"line": 247, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/about/our-principals.tsx"], "sourcesContent": ["import React from 'react';\r\n\r\nconst OurPrincipals = () => {\r\n  const coreValues = [\r\n    {\r\n      title: \"PROFESSIONALISM\",\r\n      description:\r\n        \"We always commit to do  our projects in a  workmanship like manner.\",\r\n    },\r\n    {\r\n      title: \"TEAMWORK\",\r\n      description:\r\n        \"We know we can only  succeed together or fail  together as a team\",\r\n    },\r\n    {\r\n      title: \"LEADERSHIP\",\r\n      description:\r\n        \"Everyone in our team is a  leader. We believe in  leading from the bottom up.\",\r\n    },\r\n    {\r\n      title: \"INTEGRITY\",\r\n      description:\r\n        \"We believe in doing things  right & within the confines  of the laws of the land\",\r\n    },\r\n    {\r\n      title: \"HONESTY\",\r\n      description:\r\n        \"We always strive to be  honest to our clients in  project delivery\",\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <section className=\"py-16 bg-white\">\r\n      <div className=\"container mx-auto px-4 lg:px-8 max-w-4xl\">\r\n        {/* Decorative line at top */}\r\n        <div className=\"flex justify-center mb-12\">\r\n          <div className=\"w-0.5 h-16 bg-gray-300\"></div>\r\n        </div>\r\n\r\n        {/* Header */}\r\n        <div className=\"text-center mb-16\">\r\n          <h2 className=\"text-4xl md:text-5xl font-light text-gray-400 tracking-wider mb-8\">\r\n            OUR PRINCIPALS\r\n          </h2>\r\n        </div>\r\n\r\n        {/* Core Values List */}\r\n        <div className=\"space-y-12\">\r\n          {coreValues.map((value, index) => (\r\n            <div key={index} className=\"text-center\">\r\n              <h3 className=\"text-xl md:text-2xl font-semibold text-gray-800 mb-4 tracking-wide\">\r\n                {value.title}\r\n              </h3>\r\n              <p className=\"text-gray-600 leading-relaxed max-w-2xl mx-auto text-sm md:text-base\">\r\n                {value.description}\r\n              </p>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default OurPrincipals;"], "names": [], "mappings": ";;;;;;AAEA,MAAM,gBAAgB;IACpB,MAAM,aAAa;QACjB;YACE,OAAO;YACP,aACE;QACJ;QACA;YACE,OAAO;YACP,aACE;QACJ;QACA;YACE,OAAO;YACP,aACE;QACJ;QACA;YACE,OAAO;YACP,aACE;QACJ;QACA;YACE,OAAO;YACP,aACE;QACJ;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;8BAIjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;kCAAoE;;;;;;;;;;;8BAMpF,8OAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,OAAO,sBACtB,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAG,WAAU;8CACX,MAAM,KAAK;;;;;;8CAEd,8OAAC;oCAAE,WAAU;8CACV,MAAM,WAAW;;;;;;;2BALZ;;;;;;;;;;;;;;;;;;;;;AAatB;uCAEe", "debugId": null}}, {"offset": {"line": 359, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/app/about/page.tsx"], "sourcesContent": ["import React from 'react'\r\nimport AboutSection from '@/components/about/about-new'\r\nimport Philosophy from '@/components/about/philosophy'\r\nimport Journey from '@/components/about/journey'\r\nimport OurPrincipals from '@/components/about/our-principals'\r\nimport AdvantagesContract from '@/components/about/advantages-contract'\r\n\r\n\r\n\r\nconst Aboutpage = () => {\r\n  return (\r\n    <main>\r\n      \r\n\r\n      <AboutSection />\r\n      <Philosophy />\r\n      <OurPrincipals />\r\n      <Journey />\r\n      <AdvantagesContract />\r\n    </main>\r\n  )\r\n}\r\n\r\nexport default Aboutpage"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;;;;;;;;;;;;AAKA,MAAM,YAAY;IAChB,qBACE,8OAAC;;0BAGC,8OAAC,sJAAY;;;;;0BACb,8OAAC,oJAAU;;;;;0BACX,8OAAC,2JAAa;;;;;0BACd,8OAAC,iJAAO;;;;;0BACR,8OAAC;;;;;;;;;;;AAGP;uCAEe", "debugId": null}}]}